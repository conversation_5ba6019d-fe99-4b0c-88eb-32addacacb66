/*	EQEMu: Everquest Server Emulator
	Copyright (C) 2001-2002 EQEMu Development Team (http://eqemulator.org)

	This program is free software; you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation; version 2 of the License.

	This program is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY except by those people which sell it, which
	are required to give you total support for your newly bought product;
	without even the implied warranty of MERCHANT<PERSON><PERSON><PERSON><PERSON> or FITNESS FOR
	A PARTICULAR PURPOSE. See the GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with this program; if not, write to the Free Software
	Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA
*/
#ifndef LANGUAGES_H
#define LANGUAGES_H
#include "../common/types.h"

#define LANG_COMMON_TONGUE	0
#define LANG_BARBARIAN		1
#define LANG_ERUDIAN		2
#define LANG_ELVISH			3
#define LANG_DARK_ELVISH	4
#define LANG_DWARVISH		5
#define LANG_TROLL			6
#define LANG_OGRE			7
#define LANG_GNOMISH		8
#define LANG_HALFLING		9
#define LANG_THIEVES_CANT	10
#define LANG_OLD_ERUDIAN	11
#define LANG_ELDER_ELVISH	12
#define LANG_FROGLOK		13
#define LANG_GOBLIN			14
#define LANG_GNOLL			15
#define LANG_COMBINE_TONGUE	16
#define LANG_ELDER_TEIRDAL	17
#define LANG_LIZARDMAN		18
#define LANG_ORCISH			19
#define LANG_FAERIE			20
#define LANG_DRAGON			21
#define LANG_ELDER_DRAGON	22
#define LANG_DARK_SPEECH	23
#define LANG_VAH_SHIR		24
#define LANG_UNKNOWN1		25
#define LANG_UNKNOWN2		26

#endif

