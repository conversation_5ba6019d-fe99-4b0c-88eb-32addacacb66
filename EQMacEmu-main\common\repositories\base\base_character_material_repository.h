/**
 * DO NOT MODIFY THIS FILE
 *
 * This repository was automatically generated and is NOT to be modified directly.
 * Any repository modifications are meant to be made to the repository extending the base.
 * Any modifications to base repositories are to be made by the generator only
 *
 * @generator ./utils/scripts/generators/repository-generator.pl
 * @docs https://eqemu.gitbook.io/server/in-development/developer-area/repositories
 */

#ifndef EQEMU_BASE_CHARACTER_MATERIAL_REPOSITORY_H
#define EQEMU_BASE_CHARACTER_MATERIAL_REPOSITORY_H

#include "../../database.h"
#include "../../strings.h"
#include <ctime>


class BaseCharacterMaterialRepository {
public:
	struct CharacterMaterial {
		uint32_t id;
		uint8_t  slot;
		uint8_t  blue;
		uint8_t  green;
		uint8_t  red;
		uint8_t  use_tint;
		uint32_t color;
	};

	static std::string PrimaryKey()
	{
		return std::string("id");
	}

	static std::vector<std::string> Columns()
	{
		return {
			"id",
			"slot",
			"blue",
			"green",
			"red",
			"use_tint",
			"color",
		};
	}

	static std::vector<std::string> SelectColumns()
	{
		return {
			"id",
			"slot",
			"blue",
			"green",
			"red",
			"use_tint",
			"color",
		};
	}

	static std::string ColumnsRaw()
	{
		return std::string(Strings::Implode(", ", Columns()));
	}

	static std::string SelectColumnsRaw()
	{
		return std::string(Strings::Implode(", ", SelectColumns()));
	}

	static std::string TableName()
	{
		return std::string("character_material");
	}

	static std::string BaseSelect()
	{
		return fmt::format(
			"SELECT {} FROM {}",
			SelectColumnsRaw(),
			TableName()
		);
	}

	static std::string BaseInsert()
	{
		return fmt::format(
			"INSERT INTO {} ({}) ",
			TableName(),
			ColumnsRaw()
		);
	}

	static CharacterMaterial NewEntity()
	{
		CharacterMaterial e{};

		e.id       = 0;
		e.slot     = 0;
		e.blue     = 0;
		e.green    = 0;
		e.red      = 0;
		e.use_tint = 0;
		e.color    = 0;

		return e;
	}

	static CharacterMaterial GetCharacterMaterial(
		const std::vector<CharacterMaterial> &character_materials,
		int character_material_id
	)
	{
		for (auto &character_material : character_materials) {
			if (character_material.id == character_material_id) {
				return character_material;
			}
		}

		return NewEntity();
	}

	static CharacterMaterial FindOne(
		Database& db,
		int character_material_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {} = {} LIMIT 1",
				BaseSelect(),
				PrimaryKey(),
				character_material_id
			)
		);

		auto row = results.begin();
		if (results.RowCount() == 1) {
			CharacterMaterial e{};

			e.id       = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.slot     = static_cast<uint8_t>(strtoul(row[1], nullptr, 10));
			e.blue     = static_cast<uint8_t>(strtoul(row[2], nullptr, 10));
			e.green    = static_cast<uint8_t>(strtoul(row[3], nullptr, 10));
			e.red      = static_cast<uint8_t>(strtoul(row[4], nullptr, 10));
			e.use_tint = static_cast<uint8_t>(strtoul(row[5], nullptr, 10));
			e.color    = static_cast<uint32_t>(strtoul(row[6], nullptr, 10));

			return e;
		}

		return NewEntity();
	}

	static int DeleteOne(
		Database& db,
		int character_material_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {} = {}",
				TableName(),
				PrimaryKey(),
				character_material_id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int UpdateOne(
		Database& db,
		const CharacterMaterial &e
	)
	{
		std::vector<std::string> v;

		auto columns = Columns();

		v.push_back(columns[1] + " = " + std::to_string(e.slot));
		v.push_back(columns[2] + " = " + std::to_string(e.blue));
		v.push_back(columns[3] + " = " + std::to_string(e.green));
		v.push_back(columns[4] + " = " + std::to_string(e.red));
		v.push_back(columns[5] + " = " + std::to_string(e.use_tint));
		v.push_back(columns[6] + " = " + std::to_string(e.color));

		auto results = db.QueryDatabase(
			fmt::format(
				"UPDATE {} SET {} WHERE {} = {}",
				TableName(),
				Strings::Implode(", ", v),
				PrimaryKey(),
				e.id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static CharacterMaterial InsertOne(
		Database& db,
		CharacterMaterial e
	)
	{
		std::vector<std::string> v;

		v.push_back(std::to_string(e.id));
		v.push_back(std::to_string(e.slot));
		v.push_back(std::to_string(e.blue));
		v.push_back(std::to_string(e.green));
		v.push_back(std::to_string(e.red));
		v.push_back(std::to_string(e.use_tint));
		v.push_back(std::to_string(e.color));

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES ({})",
				BaseInsert(),
				Strings::Implode(",", v)
			)
		);

		if (results.Success()) {
			e.id = results.LastInsertedID();
			return e;
		}

		e = NewEntity();

		return e;
	}

	static int InsertMany(
		Database& db,
		const std::vector<CharacterMaterial> &entries
	)
	{
		std::vector<std::string> insert_chunks;

		for (auto &e: entries) {
			std::vector<std::string> v;

			v.push_back(std::to_string(e.id));
			v.push_back(std::to_string(e.slot));
			v.push_back(std::to_string(e.blue));
			v.push_back(std::to_string(e.green));
			v.push_back(std::to_string(e.red));
			v.push_back(std::to_string(e.use_tint));
			v.push_back(std::to_string(e.color));

			insert_chunks.push_back("(" + Strings::Implode(",", v) + ")");
		}

		std::vector<std::string> v;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES {}",
				BaseInsert(),
				Strings::Implode(",", insert_chunks)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static std::vector<CharacterMaterial> All(Database& db)
	{
		std::vector<CharacterMaterial> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{}",
				BaseSelect()
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			CharacterMaterial e{};

			e.id       = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.slot     = static_cast<uint8_t>(strtoul(row[1], nullptr, 10));
			e.blue     = static_cast<uint8_t>(strtoul(row[2], nullptr, 10));
			e.green    = static_cast<uint8_t>(strtoul(row[3], nullptr, 10));
			e.red      = static_cast<uint8_t>(strtoul(row[4], nullptr, 10));
			e.use_tint = static_cast<uint8_t>(strtoul(row[5], nullptr, 10));
			e.color    = static_cast<uint32_t>(strtoul(row[6], nullptr, 10));

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static std::vector<CharacterMaterial> GetWhere(Database& db, const std::string &where_filter)
	{
		std::vector<CharacterMaterial> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {}",
				BaseSelect(),
				where_filter
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			CharacterMaterial e{};

			e.id       = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.slot     = static_cast<uint8_t>(strtoul(row[1], nullptr, 10));
			e.blue     = static_cast<uint8_t>(strtoul(row[2], nullptr, 10));
			e.green    = static_cast<uint8_t>(strtoul(row[3], nullptr, 10));
			e.red      = static_cast<uint8_t>(strtoul(row[4], nullptr, 10));
			e.use_tint = static_cast<uint8_t>(strtoul(row[5], nullptr, 10));
			e.color    = static_cast<uint32_t>(strtoul(row[6], nullptr, 10));

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static int DeleteWhere(Database& db, const std::string &where_filter)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {}",
				TableName(),
				where_filter
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int Truncate(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"TRUNCATE TABLE {}",
				TableName()
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int64 GetMaxId(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COALESCE(MAX({}), 0) FROM {}",
				PrimaryKey(),
				TableName()
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

	static int64 Count(Database& db, const std::string &where_filter = "")
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COUNT(*) FROM {} {}",
				TableName(),
				(where_filter.empty() ? "" : "WHERE " + where_filter)
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

};

#endif //EQEMU_BASE_CHARACTER_MATERIAL_REPOSITORY_H
