


#ifndef RULE_CATEGORY
#define RULE_CATEGORY(name)
#endif
#ifndef RULE_INT
#define RULE_INT(cat, rule, default_value, notes)
#endif
#ifndef RULE_REAL
#define RULE_REAL(cat, rule, default_value, notes)
#endif
#ifndef RULE_BOOL
#define RULE_BOOL(cat, rule, default_value, notes)
#endif
#ifndef RULE_STRING
#define RULE_STRING(cat, rule, default_value, notes)
#endif
#ifndef RULE_CATEGORY_END
#define RULE_CATEGORY_END()
#endif




RULE_CATEGORY(Character)
RULE_BOOL(Character, CanCreate, true, "")
RULE_INT ( Character, MaxLevel, 65, "")
RULE_INT(Character, MaxBetaBuffLevel, 25, "25")
RULE_BOOL( Character, PerCharacterQglobalMaxLevel, false, "This will check for qglobal 'CharMaxLevel' character qglobal (Type 5), if player tries to level beyond that point, it will not go beyond that level")
RULE_BOOL( Character, PerCharacterBucketMaxLevel, false, "This will check for data bucket 'CharMaxLevel', if player tries to level beyond that point, it will not go beyond that level")
RULE_INT ( Character, MaxExpLevel, 0, "Sets the Max Level attainable via Experience")
RULE_INT ( Character, DeathExpLossLevel, 10, "Any level greater than this will lose exp on death")
RULE_INT ( Character, DeathExpLossMaxLevel, 255, "Any level greater than this will no longer lose exp on death")
RULE_INT ( Character, DeathItemLossLevel, 10, "")
RULE_INT ( Character, CorpseDecayTimeMS, 604800000, "7 days")
RULE_INT ( Character, EmptyCorpseDecayTimeMS, 10800000, "3 hours")
RULE_INT ( Character, CorpseResTimeMS, 10800000, "time before can not res corpse(3 hours)")
RULE_INT ( Character, DuelCorpseResTimeMS, 600000, "time before cant res corpse after a duel (10 minutes)")
RULE_INT ( Character, CorpseOwnerOnlineTimeMS, 30000, "how often corpse will check if its owner is online")
RULE_BOOL( Character, LeaveCorpses, true, "")
RULE_BOOL( Character, LeaveNakedCorpses, true, "")
RULE_INT ( Character, MaxDraggedCorpses, 2, "")
RULE_REAL( Character, DragCorpseDistance, 400, "If the corpse is <= this distance from the player, it won't move")
RULE_REAL( Character, ExpMultiplier, 1.0, "")
RULE_REAL( Character, AAExpMultiplier, 1.0, "")
RULE_REAL( Character, GroupExpMultiplier, 1.0, "")
RULE_REAL( Character, RaidExpMultiplier, 0.6, "showeq forum says raid exp was 60%")
RULE_REAL ( Character, EXPLossMultiplier, 1.0, "")
RULE_INT ( Character, AutosaveIntervalS, 240, "0=disabled")
RULE_BOOL( Character, HealOnLevel, false, "")
RULE_BOOL( Character, ManaOnLevel, false, "")
RULE_BOOL( Character, FeignKillsPet, false, "")
RULE_INT(Character, ItemManaRegenCap, 15, "")
RULE_INT(Character, ItemATKCap, 250, "")
RULE_INT ( Character, HasteCap, 100, "Haste cap for non-v3(overhaste) haste.")
RULE_BOOL ( Character, BindAnywhere, false, "")
RULE_INT ( Character, MaxFearDurationForPlayerCharacter, 4, "numbers of tics, each tic calculates every 6 seconds.")
RULE_INT ( Character, MaxCharmDurationForPlayerCharacter, 21, "")
RULE_INT ( Character, BaseHPRegenBonusRaces, 4352, "a bitmask of race(s) that receive the regen bonus. Iksar (4096) & Troll (256) = 4352. see common/races.h for the bitmask values")
RULE_BOOL ( Character, ItemCastsUseFocus, false, "If true, this allows item clickies to use focuses that have limited max levels on them")
RULE_INT ( Character, MinStatusForNoDropExemptions, 80, "This allows status x and higher to trade no drop items.")
RULE_INT ( Character, SkillCapMaxLevel, 65, "Sets the Max Level used for Skill Caps (from skill_caps table). -1 makes it use MaxLevel rule value. It is set to 75 because PEQ only has skillcaps up to that level, and grabbing the players' skill past 75 will return 0, breaking all skills past that level. This helps servers with obsurd level caps (75+ level cap) function without any modifications.")
RULE_INT ( Character, StatCap, 0, "")
RULE_BOOL ( Character, CheckCursorEmptyWhenLooting, true, "If true, a player cannot loot a corpse (player or NPC) with an item on their cursor")
RULE_BOOL ( Character, MaintainIntoxicationAcrossZones, true, "If true, alcohol effects are maintained across zoning and logging out/in.")
RULE_BOOL ( Character, EnableDiscoveredItems, false, "If enabled, it enables EVENT_DISCOVER_ITEM and also saves character names and timestamps for the first time an item is discovered.")
RULE_BOOL ( Character, KeepLevelOverMax, false, "Don't delevel a character that has somehow gone over the level cap")
RULE_INT ( Character, BaseInstrumentSoftCap, 36, "Softcap for instrument mods, 36 commonly referred to as 3.6 as well.")
RULE_REAL (Character, BaseRunSpeed, 0.7, "")
RULE_REAL(Character, EnvironmentDamageMulipliter, 1, "")
RULE_BOOL(Character, ForageNeedFoodorDrink, false, "")
RULE_BOOL (Character, DisableAAs, false, "Disables server side AA support, since the client allows some AA activity through even with a pre-Luclin expansion set.")
RULE_BOOL ( Character, SacrificeCorpseDepop, false, "If true, Sacrificed corpses will depop 3 minutes after they become empty in Pok, Nexus, or Bazaar")
RULE_INT ( Character, DefaultExpansions, 15, "When a new account is created, this is the default bitmask expansions it is given. 1 Kunark 2 Velious 4 Luclin 8 PoP.")
RULE_CATEGORY_END()

RULE_CATEGORY( Guild )
RULE_INT ( Guild, MaxGuilds, 32, "")
RULE_CATEGORY_END()

RULE_CATEGORY( Skills )
RULE_INT ( Skills, MaxTrainTradeskills, 21, "")
RULE_INT ( Skills, MaxTrainSpecializations, 50, "Max level a GM trainer will train casting specializations")
RULE_INT ( Skills, LangSkillUpModifier, 70, "skill ups for skills with value under 100")
RULE_REAL ( Skills, SkillUpModifier, 1.0, "combat skill-up rate multiplier.  1.0 = accurate skill-up rate for AK/old EQ")
RULE_CATEGORY_END()

RULE_CATEGORY( Pets )
RULE_REAL( Pets, AttackCommandRange, 200, "")
RULE_BOOL( Pets, UnTargetableSwarmPet, false, "")
RULE_CATEGORY_END()

RULE_CATEGORY(GM)
RULE_INT(GM, GMWhoList, 80, "")
RULE_INT ( GM, MinStatusToUseGMItem, 80, "")
RULE_INT ( GM, MinStatusToZoneAnywhere, 250, "")
RULE_INT(GM, MinStatusToLevelTarget, 100, "")
RULE_CATEGORY_END()

RULE_CATEGORY( World )
RULE_INT ( World, ZoneAutobootTimeoutMS, 60000, "")
RULE_INT ( World, ClientKeepaliveTimeoutMS, 65000, "")
RULE_INT ( World, MaxClientsPerIP, -1, "Maximum number of clients allowed to connect per IP address if account status is < AddMaxClientsStatus. Default value: -1 (feature disabled)")
RULE_INT(  World, MaxMulesPerIP, -1, "Maximum number of mules allowed to connect per IP address if account status is < AddMaxClientsStatus. Default value: -1 (feature disabled)")
RULE_BOOL ( World, UseBannedIPsTable, false, "Toggle whether or not to check incoming client connections against the banned_ips table. Set this value to false to disable this feature.")
RULE_INT ( World, ExemptMaxClientsStatus, -1, "Exempt accounts from the MaxClientsPerIP and AddMaxClientsStatus rules, if their status is >= this value. Default value: -1 (feature disabled)")
RULE_INT ( World, AddMaxClientsPerIP, -1, "Maximum number of clients allowed to connect per IP address if account status is < ExemptMaxClientsStatus. Default value: -1 (feature disabled)")
RULE_INT ( World, AddMaxClientsStatus, -1, "Accounts with status >= this rule will be allowed to use the amount of accounts defined in the AddMaxClientsPerIP. Default value: -1 (feature disabled)")
RULE_BOOL ( World, MaxClientsSetByStatus, false, "If True, IP Limiting will be set to the status on the account as long as the status is > MaxClientsPerIP")
RULE_BOOL ( World, ClearTempMerchantlist, false, "Clears temp merchant items when world boots.")
RULE_INT ( World, AccountSessionLimit, 1, "Max number of characters allowed on at once from a single account (-1 is disabled)")
RULE_INT ( World, ExemptAccountLimitStatus, 100, "Min status required to be exempt from multi-session per account limiting (-1 is disabled)")
RULE_BOOL ( World, GMAccountIPList, false, "Check ip list against GM Accounts, AntiHack GM Accounts.")
RULE_INT ( World, MinGMAntiHackStatus, 1, "Minimum GM status to check against AntiHack list")
RULE_INT ( World, PVPSettings, 0, "Sets the PVP settings for the server, 1 = Rallos Zek RuleSet, 2 = Tallon/Vallon Zek Ruleset, 4 = Sullon Zek Ruleset, 6 = Discord Ruleset, anything above 6 is the Discord Ruleset without the no-drop restrictions removed. TODO: Edit IsAttackAllowed in Zone to accomodate for these rules.")
RULE_INT (World, FVNoDropFlag, 0, "Sets the Firiona Vie settings on the client. If set to 2, the flag will be set for GMs only, allowing trading of no-drop items.")
RULE_BOOL (World, IPLimitDisconnectAll, false, "")
RULE_INT (World, TellQueueSize, 200, "")
RULE_BOOL (World, AdjustRespawnTimes, false, "Determines if spawntimes with a boot time variable take effect or not. Set to false in the db for emergency patches.")
RULE_INT( World, MaximumQuestErrors, 30, "Changes the maximum number of quest errors that can be displayed in #questerrors, default is 30")
RULE_INT (World, BootHour, 0, "Sets the in-game hour world will set when it first boots. 0-24 are valid options, where 0 disables this rule.")
RULE_INT(World, StreamDataRate, 50, "Sets the datarate for EQOldStream. Defaults to 50.")
RULE_INT(World, WhoListLimit, 20, "The max players returned in /who all.")
RULE_INT(World, MuleToonLimit, 8, "The number of characters a mule account can create/access.")
RULE_BOOL(World, DontBootDynamics, false, "If true, dynamic zones will not boot when a player tries to enter them.")
RULE_REAL(World, CurrentExpansion, 6.0, "")
RULE_INT(World, WorldClientLinkdeadMS, 45000, "the time before a client times out as stale from world. 45s default.")
RULE_INT(World, ClientTimeoutStaleAmount, 3, "")
RULE_BOOL(World, EnableDevTools, true, "Enable or Disable the Developer Tools globally (Most of the time you want this enabled)")
RULE_BOOL(World, UseOldShadowKnightClassExport, true, "Disable to have Shadowknight show as Shadow Knight (live-like)")
RULE_STRING(World, MOTD, "", "Server MOTD sent on login, change from empty to have this be used instead of variables table 'motd' value")
RULE_CATEGORY_END()

RULE_CATEGORY( Zone )
RULE_INT ( Zone, ClientLinkdeadMS, 180000, "the time a client remains link dead on the server after a sudden disconnection")
RULE_BOOL ( Zone, EnableShadowrest, true, "enables or disables the shadowrest zone feature for player corpses. Default is turned on.")
RULE_BOOL ( Zone, UsePlayerCorpseBackups, true, "Keeps backups of player corpses.")
RULE_INT ( Zone, AutoShutdownDelay, 60000, "How long a dynamic zone stays loaded while empty (milliseconds).")
RULE_INT ( Zone, PEQZoneReuseTime, 900, "How long, in seconds, until you can reuse the #peqzone command.")
RULE_INT ( Zone, PEQZoneDebuff1, 4454, "First debuff casted by #peqzone Default is Cursed Keeper's Blight.")
RULE_INT ( Zone, PEQZoneDebuff2, 2209, "Second debuff casted by #peqzone Default is Tendrils of Apathy.")
RULE_BOOL ( Zone, UsePEQZoneDebuffs, true, "Will determine if #peqzone will debuff players or not when used.")
RULE_BOOL ( Zone, LevelBasedEXPMods, true, "Allows you to use the level_exp_mods table in consideration to your players EXP hits.  This is used for Hell Level Balance Modifiers used for Luclin era hell level smoothing.  Make all the rows 1.0 for Velious era exp")
RULE_INT ( Zone, WeatherTimer, 600, "Weather timer when no duration is available")
RULE_INT (Zone, SpawnEventMin, 5, "When strict is set in spawn_events, specifies the max EQ minutes into the trigger hour a spawn_event will fire.")
RULE_REAL ( Zone, GroupEXPRange, 500, "")
RULE_BOOL ( Zone, IdleWhenEmpty, true, "After timer is expired, if zone is empty it will idle. Boat zones are excluded, as this will break boat functionality.")
RULE_INT ( Zone, IdleTimer, 600000, "600000ms = 10 minutes")
RULE_INT ( Zone, BoatDistance, 50, "In zones where boat name is not set in the PP, this is how far away from the boat the client must be to move them to the boat's current location.")
RULE_BOOL ( Zone, EnableNexusPortals, false, "")
RULE_INT ( Zone, NexusTimer, 900000, "Nexus timer in ms. Defaults to 15 minutes.")
RULE_INT ( Zone, NexusScionTimer, 900000, "Nexus timer in ms. Defaults to 15 minutes.")
RULE_BOOL ( Zone, EnableNexusPortalsOnExpansion, true, "Nexus enables When Luclin is the current expansion")
RULE_INT(Zone, GlobalLootMultiplier, 1, "Sets Global Loot drop multiplier for database based drops, useful for double, triple loot etc")
RULE_REAL(Zone, HotZoneBonus, 0.75, "Value which is added to the experience multiplier. This also applies to AA experience.")
RULE_BOOL (Zone, KillProcessOnDynamicShutdown, true, "When process has booted a zone and has hit its zone shut down timer, it will hard kill the process to free memory back to the OS")

RULE_CATEGORY_END()

RULE_CATEGORY( AlKabor )
RULE_BOOL( AlKabor, AllowPetPull, false, "Allow Green Pet Pull (AK behavior is true)")
RULE_BOOL( AlKabor, AllowTickSplit, false, "AK behavior is true")
RULE_BOOL ( AlKabor, StripBuffsOnLowHP, true, "AK behavior is true")
RULE_BOOL ( AlKabor, OutOfRangeGroupXPBonus, false, "AK behavior is true. When true, players out of range of the kill will still count towards the group bonus. (They will not receive XP.)  This enables the exploit that allowed a soloing player to recieve the entire 2.6x group bonus")
RULE_BOOL(AlKabor, ClassicGroupEXPBonuses, false, "AK behavior is false.  If true, use the Clsasic to 1 month into Velious era group exp bonus. (2% for 2 members, up to 10% for 6 members)  This was the case from Launch until to Jan 2001 on PC.  False will use the Velious double bonus rules if enabled, or the post June 2003 bonus of 10% per additional member up to 80% if both Velious and Classic rules are disabled.")
RULE_BOOL ( AlKabor, VeliousGroupEXPBonuses, false, "AK behavior is false.  If true, use the Velious to mid PoP era group exp bonus. (2% for 2 members, up to 20% for 6 members)  This was the case from Jan 2001 to June 2003 on PC.  (prior to that was a 6 man bonus of 10%)  False will use the post June 2003 bonus of 20% per aditional member up to 80%")
RULE_BOOL ( AlKabor, GroupEXPBonuses, false, "AK behavior is true. When true, the broken 4-6 member group bonuses will be used.  Note: ClassicGroupEXPBonuses must be false for this to work")
RULE_BOOL ( AlKabor, Count6thGroupMember, true, "AK behavior is true. When true, the 6th member of the group will count towards the split, thus reducing the XP everybody gets.  Note: this should be false if using the post June 2003 PC era exp rules is intended")
RULE_BOOL ( AlKabor, GreensGiveXPToGroup, true, "AK behavior is true. When true, lower level players will receive group XP when a green mob to the higher players is killed.")
RULE_BOOL (AlKabor, GreenExpBonus, true, "AK is (supposedly) true.  When true, any group member that the mob is green to will have their weighted division split reduced to that of the highest level player who does get exp, resulting in a larger share to those who get exp from the mob.  This results in higher level players being able to powerlevel even more effectively.  no hard evidence is known of this however")
RULE_BOOL( AlKabor, AllowCharmPetRaidTanks, true, "AK behavior is true.  If false, NPCs will ignore charmed pets once MaxEntitiesCharmTanks players get on an NPC's hate list as per April 2003 patch.")
RULE_INT( AlKabor, MaxEntitiesCharmTanks, 8, "If AllowCharmPetRaidTanks is false, this is the max number of entities on an NPC's hate list before the NPC will ignore charmed pets.  April 2003 patch set this to 4 on Live.")
RULE_BOOL ( AlKabor, AllowPriceIncWhileBrowsing, true, "AK behavior is true. If true, this allows Bazaar traders to increase the price of an item while another player is browsing their wares.")
RULE_INT ( AlKabor, LevelCorpsesAlwaysSpawn, 55, "AK behavior is 55. The level NPC corpses will not poof even if a NPC was top hate/final blow.")
RULE_BOOL( AlKabor, NPCsSendHPUpdatesPerTic, false, "AK behavior is true. NPCs will only send HP updates every tic or when targeted instead of real time.")
RULE_BOOL( AlKabor, NoMaxWhoGuild, true, "AK behavior is false. If true, /who all guild# will return without a limit.")
RULE_BOOL(AlKabor, ServerExpBonus, true, "AK behavior is true.  This grants a multiplicative 20% experience bonus that was unique to AK")
RULE_REAL(AlKabor, LightBlueExpMod, 100.0, "Make sure they are all 100.0 for accurate experience gains.  Exp scaling by level is handled in the exp routines")
RULE_REAL(AlKabor, BlueExpMod, 100.0, "")
RULE_REAL(AlKabor, WhiteExpMod, 100.0, "")
RULE_REAL(AlKabor, YellowExpMod, 100.0, "")
RULE_REAL(AlKabor, RedExpMod, 100.0, "")
RULE_BOOL(AlKabor, RememberAir, true, "AK behavior is true. If zoning from one underwater area to another, remember air_remaining value. If false, it's set to 100.")
RULE_BOOL(AlKabor, ClickyHateExploit, false, "AK behavior is true. When true, it allows Invis Vs Animals clicky items to generate massive hate.")
RULE_BOOL(AlKabor, InvulnHateReduction, false, "DA spells seemed to have reduced hate on AK by an unknown amount; if true this will halve the hate")
RULE_BOOL(AlKabor, ReduceAEExp, true, "AK behavior is true.  Reduce the amount of experience gained when NPC is killed with a PBAoE spell.  Applies to NPCs around level 35 to 55")
RULE_BOOL(AlKabor, RaceEffectsAASplit, true, "AK behavior is true.  If true then race exp penalties (and bonus in case of halfling) will modify AA Exp ONLY when AA Exp is under 100%")
RULE_BOOL(AlKabor, NoDropRemoveTradeskill, true, "AK behavior is true.  If true then no drop items will be delete if container is closed.  If false, it will not delete for the original player only.")
RULE_BOOL(AlKabor, ReducedMonkAC, true, "AK behavior is true.  Monks had a low AC softcap from October 16 2002 to April 8 2003 which made them squishy.  Sony partially unnerfed them in April 03.")
RULE_BOOL(AlKabor, BlockProjectileCorners, true, "AK behavior is true.  If an NPC was in a corner, arrows and bolts would not hit them.")
RULE_BOOL(AlKabor, BlockProjectileWalls, true, "AK behavior is true.  If an NPC was walled, then arrows and bolts had to be fired from an angle parallel to the wall in order to hit them. (if this is true, corners will also block)")
RULE_BOOL(AlKabor, GreenmistHack, true, "Greenmist recourse didn't work on AK.  The spell data is messed up so it's not properly fixable without modifying the client.  This enables a partial workaround that is not AKurate but provides some benefit to players using this weapon.")
RULE_BOOL(AlKabor, EnableMobLevelModifier, true, "AK behavior is true.  If true, enable the September 4 & 6 2002 patch exp modifications that granted a large experience bonus to kills within +/-5 levels of the player for level 51+ players")
RULE_BOOL(AlKabor, EnableEraItemRules, false, "AK behavior is false. If true, disable item data in the era they did not exist in.")
RULE_BOOL(AlKabor, EnableLuclinHarmonyResistOverride, true, "AK behavior is true. If true, enable the late Luclin Harmony resist override.")
RULE_BOOL(AlKabor, EnableLatePlanesHarmonyNerf, true, "AK behavior is true. If true, enable the late Planes of Power Harmony nerf")
RULE_CATEGORY_END()

RULE_CATEGORY(Quarm)
RULE_BOOL(Quarm, CursorAllowDuplicateItems, true, "Allows duplicate item IDs to stack on the cursor.  Set to false to emulate very early clients.")
RULE_BOOL(Quarm, EnableQuakes, true, "Quarm default is true. If false, disable the auto-quake system.")
RULE_BOOL(Quarm, EnableQuakeDowntimeRecovery, false, "24 Hour")
RULE_INT(Quarm, QuakeMinVariance, 604800, "7 Days")
RULE_INT(Quarm, QuakeMaxVariance, 864000, "10 Days")
RULE_INT(Quarm, QuakeRepopDelay, 900, "15 Minutes")
RULE_INT(Quarm, QuakeEndTimeDuration, 84600, "24 Hours")
RULE_INT(Quarm, RespawnReductionLowerBoundMin, 60001, "60 to 400 seconds")
RULE_INT(Quarm, RespawnReductionHigherBoundMin, 10000, "10 to 60 seconds")
RULE_INT(Quarm, RespawnReductionLowerBoundMax, 400000, "60 to 400 seconds")
RULE_INT(Quarm, RespawnReductionHigherBoundMax, 60000, "10 to 60 seconds")
RULE_INT(Quarm, RespawnReductionLowerBound, 12000, "12s")
RULE_INT(Quarm, RespawnReductionHigherBound, 60000, "60s")
RULE_INT(Quarm, RespawnReductionDungeonLowerBoundMin, 300000, "300 to 899 seconds")
RULE_INT(Quarm, RespawnReductionDungeonHigherBoundMin, 900000, "900 to 2400 seconds")
RULE_INT(Quarm, RespawnReductionDungeonLowerBoundMax, 899000, "300 to 899 seconds")
RULE_INT(Quarm, RespawnReductionDungeonHigherBoundMax, 2400000, "900 to 2400 seconds")
RULE_INT(Quarm, RespawnReductionDungeonLowerBound, 300000, "300s")
RULE_INT(Quarm, RespawnReductionDungeonHigherBound, 500000, "500s")
RULE_INT(Quarm, HardcoreDeathLevel, 1, "Defaults to level 1. The level in which someone will be wiped upon dying if hardcore.")
RULE_INT(Quarm, HardcoreDeathBroadcastLevel, 15, "Defaults to level 15. A serverwide message is generated when someone dies after this level milestone.")
RULE_BOOL(Quarm, EnableRespawnReductionSystem, false, "10 to 60 seconds")
RULE_BOOL(Quarm, DeleteHCCharactersAfterDeath, false, "If true, characters whom are flagged as hardcore will be deleted after their untimely death with no way to recover them.")
RULE_BOOL(Quarm, EnableSpellSixLevelRule, false, "")
RULE_BOOL(Quarm, UseClassicResists, true, "")
RULE_BOOL(Quarm, PreLuclinDiseaseCounterAggro, true, "")
RULE_INT(Quarm, RespawnReductionNewbiePullLimit, 4, "")
RULE_INT(Quarm, RespawnReductionStandardPullLimit, 15, "")
RULE_INT(Quarm, RespawnReductionDungeonPullLimit, 15, "")
RULE_BOOL(Quarm, EnablePetExperienceSplit, true, "Accurate from Classic Launch until Luclin. Enables pet experience weights in groups (or 50% if solo) if a single pet deals more than 50% damage to a target out of all contributing damage.")
RULE_BOOL(Quarm, EnableChecksumEnforcement, true, "Enables or disables the dll checksum enforcement.")
RULE_INT(Quarm, GuildFTELockoutTimeMS, 300000, "")
RULE_BOOL(Quarm, VeliousEraAggroCaps, false, "Use Velious Era Aggro ")
RULE_INT(Quarm, GuildFTEDisengageTimeMS, 60000, "")
RULE_BOOL(Quarm, EnableProjectSpeedie, false, "")
RULE_REAL(Quarm, SpeedieDistThreshold, 5.0, "")
RULE_REAL(Quarm, SpeedieSecondElapsedThreshold, 1.0, "")
RULE_REAL(Quarm, SpeedieDistFromExpectedThreshold, 125, "")
RULE_REAL(Quarm, SpeedieDistFromZonePointThreshold, 200.0, "")
RULE_REAL(Quarm, SpeedieDistFromBoatThreshold, 200.0, "")
RULE_REAL(Quarm, SpeedieSlowerDistDivTime, 125.0, "")
RULE_REAL(Quarm, SpeedieHigherDistDivTime, 140.0, "")
RULE_REAL(Quarm, SpeedieBardDistDivTime, 160.0, "")
RULE_REAL(Quarm, SpeedieHighSpeedThreshold, 1.1, "")
RULE_REAL(Quarm, SpeedieBardSpeedThreshold, 1.3, "")
RULE_INT(Quarm, MaxTradeskillCap, 200, "During Classic until late Kunark, this should remain 200.")
RULE_BOOL(Quarm, NoPlayerDamagePetPenalty, false, "During Classic through Velious, true in Luclin+")
RULE_BOOL(Quarm, EnableBardDamagingAOECap, true, "")
RULE_INT(Quarm, BardDamagingAOECap, 4, "")
RULE_BOOL(Quarm, CorpseUnlockIsHalvedDecayTime, true, "")
RULE_INT(Quarm, AccidentalFallTimerMS, 15000, "Length of initial zonein fall protection, in MS.")
RULE_REAL(Quarm, AccidentalFallUnitDist, 50.0, "Length of initial zonein fall protection, in MS.")
RULE_BOOL(Quarm, ThanksgivingExpBonus, false, "")
RULE_REAL(Quarm, ThanksgivingExpBonusOutdoorAmt, 0.20, "")
RULE_REAL(Quarm, FlyingRaceExpBonus, 0.50, "")
RULE_REAL(Quarm, InstanceZEMOverride, 20.0, "")
RULE_REAL(Quarm, PVPInstanceZEMOverride, 110.0, "")
RULE_INT(Quarm, AntiSpamMuteInSeconds, 900, "Defaults to 15 minutes. Live will likely adjust this")
RULE_BOOL(Quarm, EnableNPCProximityAggroSystem, false, "Classic behavior is true. Live Quarm has this false by default. CSR complaints about training warranted this behavior.")
RULE_INT(Quarm, AutomatedRaidRotationRaidGuildLevelRequirement, 30, "Required level to participate in raid content.")
RULE_INT(Quarm, AutomatedRaidRotationRaidGuildMemberCountRequirement, 12, "Required amount of members to participate in a raid encounter. Not all of these must be in the same guild (see below rule.)")
RULE_INT(Quarm, AutomatedRaidRotationRaidNonMemberCountRequirement, 18, "Required amount of same-guild members to participate in a raid encounter. These must be in the same guild, and one officer from the current guild must be in the raid.")
RULE_INT(Quarm, MinStatusToZoneIntoAnyGuildZone, 100, "Required amount of same-guild members to participate in a raid encounter. These must be in the same guild, and one officer from the current guild must be in the raid.")
RULE_BOOL(Quarm, EnableGuildZoneRequirementOnEntry, false, "Classic behavior is true. Live Quarm has this false by default. CSR complaints about training warranted this behavior.")
RULE_INT(Quarm, AOEThrottlingMaxAOETargets, 50, "This will curb nonsense with performance issues relating to amount of targets if the amount of clients exceeds 300 in a single zone.")
RULE_INT(Quarm, AOEThrottlingMaxClients, 300, "This will curb nonsense with performance issues relating to amount of targets if the amount of clients exceeds 300 in a single zone.")
RULE_INT(Quarm, AOEMaxHostilePBAOETargets, 30, "")
RULE_BOOL(Quarm, LimitPBAOEDetrimentalSpells, true, "")
RULE_INT(Quarm, EnableLuclinEraShieldACOvercap, false, "")
RULE_INT(Quarm, ClientInstanceBootGraceMS, 60000, "")
RULE_INT(Quarm, DeletedCharacterMarkLevel, 10, "")
RULE_BOOL(Quarm, EastCommonMules, true, "")
RULE_BOOL(Quarm, IncludeMulesInServerCount, false, "")
RULE_BOOL(Quarm, ErollsiDayEvent, false, "")
RULE_BOOL(Quarm, ShowTemporaryLastNames, true, "Shows last names that are configured by the Title NPC.")
RULE_BOOL(Quarm, RestrictIksarsToKunark, false, "")
RULE_BOOL(Quarm, AllowBypassMaxClientsOnWorldEnter, false, "")
RULE_BOOL(Quarm, InstanceAlwaysHasMinimumSpawnTime, true, "")
RULE_INT(Quarm, InstanceMinimumSpawnTime, 64800000, "")
RULE_BOOL(Quarm, EnableQuestBasedXPLimit, true, "Whether or not to enable the Quest XP killswitch. Use on major content launches to avoid abuse.") // Whether or not to enable the Quest XP killswitch. Use on major content launches to avoid abuse.
RULE_INT(Quarm, QuestBasedXPLimitLevel, 51, "For Kunark. Adjust for POP Launch.")
RULE_BOOL(Quarm, BonusGroupEXPOverride, false, "Quarm's custom group EXP bonus override. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP2MemberOverride, 1.02, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP3MemberOverride, 1.06, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP4MemberOverride, 1.10, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP5MemberOverride, 1.14, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP6MemberOverride, 1.20, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_REAL(Quarm, BonusGroupEXP7MemberOverride, 1.24, "Quarm's custom group EXP bonus override for x members. For server events that directly target group bonuses.")
RULE_INT(Quarm, MaxTimeSpentProcessingConns, 100, "")
RULE_BOOL(Quarm, ClientFactionOverride, false, "Enables or disables client personal faction adjustment")
RULE_REAL(Quarm, ClientFactionMultiplier, 2.0, "Client personal faction adjustment multiplier")
RULE_BOOL(Quarm, LichDamageMitigation, false, "Quarm lich hack to make PoP era lich spell data cause the correct live era pvp mitigated damage to self.")
RULE_BOOL(Quarm, ClientBeneficialSpellDurationModifier, false, "Enables or disables duration modification for player beneficial spells")
RULE_BOOL(Quarm, ClientDetrimentalSpellDurationModifier, false, "Enables or disables duration modification for player detrimental spells")
RULE_BOOL(Quarm, NPCBeneficialSpellDurationModifier, false, "Enables or disables duration modification for NPC beneficial spells")
RULE_BOOL(Quarm, NPCDetrimentalSpellDurationModifier, false, "Enables or disables duration modification for NPC detrimental spells")
RULE_BOOL(Quarm, EnableGlobalSkillupDifficultyAdjustments, false, "Enables or disables skillup modifiers globally for players. Lower = Faster.")
RULE_REAL(Quarm, GlobalSkillupDifficultyAdjustment, 1.0f, "Multiplier forEnableGlobalSkillupDifficultyAdjustments - default is 1.0f. 0.5 would be double (Lower = Faster)")
RULE_REAL(Quarm, GlobalTradeSkillupDifficultyAdjustment, 1.0f, "Tradeskill multiplier forEnableGlobalSkillupDifficultyAdjustments - default is 1.0f. 0.5 would be double (Lower = Faster)")
RULE_BOOL(Quarm, EnableQuestExpMultiplier, false, "")
RULE_REAL(Quarm, QuestExpMultiplier, 1.0, "")
RULE_BOOL(Quarm, EnableBardInstagibLimit, false, "")
RULE_INT(Quarm, BardInstagibPullLimit, 16, "")
RULE_REAL(Quarm, BardInstagibHPRatio, 95.0, "")
RULE_INT(Quarm, BardInstagibResummonTimerMS, 6500, "")
RULE_BOOL(Quarm, BardInstagibReverseSummon, true, "Should NPCs reverse summon themselves to the Bard")
RULE_STRING(Quarm, ChatHostname, "pq.projectquarm.com", "")
RULE_INT(Quarm, ChatPort, 9876, "")
RULE_STRING(Quarm, ChatShortName, "takp", "")
RULE_STRING(Quarm, AllianceChannelName, "Alliance", "")
RULE_STRING(Quarm, AllianceChannelReplacementName, "General", "")
RULE_STRING(Quarm, NoChannelName, "None", "")
RULE_STRING(Quarm, LfgChannelName, "Lfg", "")
RULE_STRING(Quarm, PortsChannelName, "Ports", "")
RULE_INT(Quarm, AllianceChannelLevelRequirement, 10, "")
RULE_INT(Quarm, LFGChannelLevelRequirement, 10, "")
RULE_INT(Quarm, RebirthTitleLevel, 60, "The minimum level before we add a title for rebirth.")
RULE_BOOL(Quarm, DruidEpicAppliesEnsnare, true, "")
RULE_BOOL(Quarm, PreNerfSneakHide, true, "This should be false during Luclin.")
RULE_BOOL(Quarm, ProjectSpeedieBroadcastUnknownZone, true, "")
RULE_INT(Quarm, BeastlordMaxLevel, 30, "")
RULE_REAL(Quarm, PVPExpLossMultiplier, 0.15, "")
RULE_INT(Quarm, MaxPVPPBAOETargets, 72, "")
RULE_REAL(Quarm, PVPMobLeashUnits, 300.0, "")
RULE_INT(Quarm, PVPInstanceMinimumSpawnTime, 72, "")
RULE_INT(Quarm, PVPInstanceGraveyardTime, 30, "")
RULE_REAL(Quarm, PVPDayVariance, 7, "")
RULE_BOOL(Quarm, EnablePVPInstances, false, "Enables or disables PVP instance book entry via guild 1.")
RULE_BOOL(Quarm, EnforceLatestDllToEnterSpecialInstances, false, "")
RULE_INT(Quarm, SpecialExpansionZoneIDBegin, 1500, "")
RULE_INT(Quarm, SpecialExpansionZoneIDEnd, 5000, "")
RULE_BOOL(Quarm, UseFixedShowHelmBehavior, true, "Fixes ShowHelm to be a personal toggle that works like other MMOs. Also adds full compatibility with Zeal's ShowHelm feature and Velious Helms support.")
RULE_INT(Quarm, WarnDllVersionBelow, 1, "Sends a Client-out-of-date warning message to clients below this dll version.")
RULE_INT(Quarm, CurrentPVPExpansion, 1, "The currently enabled expansion for PVP zones.")
RULE_INT(Quarm, SharedBankBags, 10, "The number of shared bank slots the client can deposit to. '0' will only allow withdrawing. '-1' competely disables the client.")
RULE_BOOL(Quarm, CustomFrogloks, true, "Enable Quarm froglok race 330.")
RULE_BOOL(Quarm, PetZoneWithOwner, true, "Enables pet zoning with its owner.")
RULE_INT(Quarm, SpellResistHighMinimumResistChance, 10, "Resist floor")
RULE_INT(Quarm, SpellResistLowMinimumResistChance, 4, "Resist floor")
RULE_INT(Quarm, SpellResistBetweenMinimumResistChance, 10, "Resist floor")
RULE_INT(Quarm, SpellResistPlayerHighMinimumResistChance, 198, "Player Resist floor")
RULE_INT(Quarm, SpellResistPlayerLowMinimumResistChance, 4, "Player Resist floor")
RULE_BOOL(Quarm, AllowGuildInstanceBoot, false, "Allow guild instance boot")
RULE_BOOL(Quarm, EnablePullLimitSystem, true, "Enable QOL Pull Limit system. Defaults to true.")
RULE_INT(Quarm, PlayerPopulationCap, 1200, "Max Players allowed in the Server. Will exclude offline Bazaar trader characters.")
RULE_BOOL(Quarm, EnableBazaar, false, "Master switch for bazaar. Set to true when Offline Traders are in and work.")
RULE_BOOL(Quarm, EnableAdminChecks, false, "Master switch for admin check enforcement")
RULE_BOOL(Quarm, EnableOfflineTraders, false, "Master switch for admin check enforcement")
RULE_INT(Quarm, BazaarAutoKickTimerS, 600, "Bazaar auto-kick timer.")
RULE_REAL(Quarm, ArcheryCorneredDamage, 1.0, "% Modifier to Archery damage of cornered targets (0.5 = 50%, 1.0 = 100%")
RULE_REAL(Quarm, ArcheryWalledDamage, 1.0, "% Modifier to Archery damage of walled targets (0.5 = 50%, 1.0 = 100%")
RULE_INT(Quarm, WorldClientInactiveTimeoutMS, 3600000, "Timeout in MS that clients can stay connected to world for")
RULE_BOOL(Quarm, EnableWorldClientInactiveTimeout, false, "Master switch for World - ClientInactiveTimeoutMS")
RULE_CATEGORY_END()

RULE_CATEGORY(SelfFound)
RULE_BOOL(SelfFound, PetLootSupport, true, "Allow solo/self-found players to loot their own SF items back from pet/npc corpses. This allows SF players to recover their items they gave to charmed pets.")
RULE_BOOL(SelfFound, TempMerchantSupport, true, "Allow solo/self-found players to repurchase their own SF Items sold to Merchant temp inventory, up to the amount of SF items sold that belonged to them. This allows SF players to vendor recharge their own items. Also makes temp inventory visible, but will get an error message if trying purchase items they are not permitted to buy.")
RULE_BOOL(SelfFound, TempMerchantFiltering, false, "Make solo/self-found players only see merchant temp inventory for their own purchasable items, hiding the rest. If disabled, they can see the whole temp inventory, but can still only purchase their valid SF items back.")
RULE_CATEGORY_END()

RULE_CATEGORY( Map )
//enable these to help prevent mob hopping when they are pathing
RULE_BOOL ( Map, FixPathingZWhenLoading, true, "increases zone boot times a bit to reduce hopping.")
RULE_BOOL ( Map, FixZWhenPathing, true, "very CPU intensive, but helps hopping with widely spaced waypoints.")
RULE_BOOL ( Map, FixPathingZOnSendTo, false, "try to repair Z coords in the SendTo routine as well.")
RULE_REAL ( Map, FixPathingZMaxDeltaSendTo, 20, "at runtime in SendTo: max change in Z to allow the BestZ code to apply.")
RULE_REAL ( Map, FixPathingZMaxDeltaLoading, 200, "while loading each waypoint: max change in Z to allow the BestZ code to apply.")
RULE_INT ( Map, FindBestZHeightAdjust, 1, "Adds this to the current Z before seeking the best Z position. If this is too high, mobs bounce when pathing.")
RULE_REAL ( Map, BestZSizeMax, 20.0, "When calculating bestz using size, this is our size cap. Setting this too high causes dragons and giants to hop.")
RULE_CATEGORY_END()

RULE_CATEGORY( Pathing )
RULE_BOOL ( Pathing, Fear, true, "Enable pathing for fear")
RULE_REAL ( Pathing, CandidateNodeRangeXY, 200, "When searching for path start/end nodes, only nodes within this range will be considered.")
RULE_REAL ( Pathing, CandidateNodeRangeZ, 25, "When searching for path start/end nodes, only nodes within this range will be considered.")
RULE_INT(Pathing, MaxNavmeshNodes, 8184, "Maximum navmesh nodes in a traversable path")
RULE_REAL(Pathing, NavmeshStepSize, 100.0f, "Step size for the movement manager")
RULE_REAL(Pathing, ShortMovementUpdateRange, 130.0f, "Range for short movement updates")
RULE_CATEGORY_END()

RULE_CATEGORY( Watermap )
// enable these to use the water detection code. Requires Water Maps generated by awater utility
// WARNING: Bestz in water is the ocean floor, so if you have NPCs that float near the top (sharks, boats) disabling these rules may break them!
RULE_BOOL ( Watermap, CheckWaypointsInWaterWhenLoading, true, "Does not apply BestZ as waypoints are loaded if they are in water")
RULE_BOOL ( Watermap, CheckForWaterOnSendTo, true, "Checks if a mob has moved into/out of water on SendTo")
RULE_BOOL ( Watermap, CheckForWaterWhenFishing, true, "Only lets a player fish near water (if a water map exists for the zone)")
RULE_REAL(Watermap, FishingRodLength, 30, "How far in front of player water must be for fishing to work")
RULE_REAL(Watermap, FishingLineLength, 100, "If water is more than this far below the player, it is considered too far to fish")
RULE_REAL(Watermap, FishingLineStepSize, 1, "Basic step size for fishing calc, too small and it will eat cpu, too large and it will miss potential water")
RULE_CATEGORY_END()

RULE_CATEGORY( Spells )
RULE_REAL ( Spells, ResistChance, 2.0, "chance to resist given no resists and same level")
RULE_INT ( Spells, WizCritLevel, 12, "level wizards first get spell crits")
RULE_INT ( Spells, TranslocateTimeLimit, 0, "If not zero, time in seconds to accept a Translocate.")
RULE_INT ( Spells, SacrificeMinLevel, 46, "first level Sacrifice will work on")
RULE_INT ( Spells, SacrificeMaxLevel, 60, "last level Sacrifice will work on")
RULE_INT ( Spells, SacrificeItemID, 9963, "Item ID of the item Sacrifice will return (defaults to an EE)")
RULE_BOOL( Spells, EnableSpellGlobals, false, "If Enabled, spells check the spell_globals table and compare character data from the quest globals before allowing that spell to scribe with scribespells")
RULE_BOOL( Spells, EnableSpellBuckets, false, "If Enabled, spells check the spell_buckets table and compare character data from their data buckets before allowing the spell to scribe with scribespells")
RULE_INT ( Spells, MaxBuffSlotsNPC, 30, "")
RULE_INT ( Spells, MaxTotalSlotsNPC, 30, "")
RULE_INT ( Spells, MaxTotalSlotsPET, 15, "")
RULE_INT ( Spells, ReflectType, 1, "0 = disabled, 1 = single target player spells only, 2 = all player spells, 3 = all single target spells, 4 = all spells")
RULE_BOOL( Spells, LiveLikeFocusEffects, true, "Determines whether specific healing, dmg and mana reduction focuses are randomized")
RULE_INT ( Spells, BaseImmunityLevel, 55, "The level that targets start to be immune to stun, fear and mez spells with a max level of 0.")
RULE_INT ( Spells, ResistFalloff, 67, "Max that level that will adjust our resist chance based on level modifiers")
RULE_INT ( Spells, CharmMinResist, 5, " When rolling charm tick save throws, this is the minimum value that resists can result to after debuffs and level advantage.  This essentially determines how good charm is")
RULE_INT ( Spells, RootBreakFromSpells, 55, "Chance for root to break when cast on.")
RULE_INT ( Spells, RootMinResist, 5, "When rolling root tick save throws, this is the minimum value that resists can result to after debuffs and level advantage.  This determines how good root is on low resist targets")
RULE_BOOL ( Spells, AdditiveBonusValues, false, "Allow certain bonuses to be calculated by adding together the value from each item, instead of taking the highest value. (ie Add together all Cleave Effects)")
RULE_BOOL ( Spells, BuffLevelRestrictions, true, "Buffs will not land on low level toons like live")
RULE_INT ( Spells, RootBreakCheckChance, 75, "Determines chance for a root break check to occur each buff tick.")
RULE_INT ( Spells, BlindBreakCheckChance, 75, "Determines chance for blind tick save throw")
RULE_INT ( Spells, FearBreakCheckChance, 75, "Determines chance for a fear break check to occur each buff tick.")
RULE_BOOL ( Spells, FocusCombatProcs, false, "Allow all combat procs to receive focus effects.")
RULE_INT ( Spells, AI_SpellCastFinishedFailRecast, 800, "AI spell recast time(MS) when an spell is cast but fails (ie stunned).")
RULE_INT ( Spells, AI_EngagedNoSpellMinRecast, 750, "AI spell recast time(MS) check when no spell is cast while engaged. (min time in random)")
RULE_INT ( Spells, AI_EngagedNoSpellMaxRecast, 2000, "AI spell recast time(MS) check when no spell is cast engaged.(max time in random)")
RULE_INT ( Spells, AI_EngagedBeneficialSelfChance, 100, "Chance during first AI Cast check to do a beneficial spell on self.")
RULE_INT ( Spells, AI_EngagedBeneficialOtherChance, 25, "Chance during second AI Cast check to do a beneficial spell on others.")
RULE_INT ( Spells, AI_EngagedDetrimentalChance, 25, "Chance during third AI Cast check for DPS classes do a determental spell on others.")
RULE_INT ( Spells, AI_EngagedDetrimentalChanceHealer, 25, "Chance during third AI Cast check for healer classes to do a determental spell on others.")
RULE_INT ( Spells, AI_EngagedDetrimentalChanceHybrid, 3, "Chance during third AI Cast check for hybrid classes to do a determental spell on others.")
RULE_INT ( Spells, AI_IdleNoSpellMinRecast, 1500, "AI spell recast time(MS) check when no spell is cast while idle. (min time in random)")
RULE_INT ( Spells, AI_IdleNoSpellMaxRecast, 6000, "AI spell recast time(MS) check when no spell is cast while idle. (max time in random)")
RULE_INT ( Spells, AI_IdleBeneficialChance, 100, "Chance while idle to do a beneficial spell on self or others.")
RULE_BOOL ( Spells, SHDProcIDOffByOne, true, "pre June 2009 SHD spell procs were off by 1, they stopped doing this in June 2009 (so UF+ spell files need this false)")
RULE_BOOL ( Spells, SwarmPetTargetLock, false, "Use old method of swarm pets target locking till target dies then despawning.")
RULE_INT ( Spells, SpellRecoveryTimer, 3500, "Begins when a cast is complete, and is checked after the next spell finishes casting. If not expired, the new spell is interrupted. Clickies are exempt.")
RULE_BOOL ( Spells, JamFestAAOnlyAffectsBard, true, "Bard Jam Fest AA only worked on bards themselves but was changed after AK's era.  Changing this to false will put the client stats out of sync with the server.")
RULE_BOOL ( Spells, ReducePacifyDuration, false, "AK and the eqmac client have 60 tick Pacify (spell 45) duration.  This rule reduces the duration to 7 ticks without desyncing the cast bar and focus effects for custom servers that want this.")
RULE_BOOL(Spells, ShowDotDmgMessages, false, "Enables LoY-era DoT damage messages. Disabled by default since this didn't exist on Al'Kabor.")
RULE_BOOL(Spells, RainPreventKill, false, "Enables blocking rain spells from killing npcs")
RULE_INT ( Spells, RainWizardResistChance, 0, "Chance for wizard rain spells to get resisted automatically")
RULE_BOOL( Spells, AllowBuffstackingPatch, true, "Allows the client/server to negotiate using the patched buffstacking logic in eqgame.dll. Both sides exchange a handshake to safely enable the patch.")
RULE_CATEGORY_END()

RULE_CATEGORY( Combat )
RULE_INT ( Combat, RogueCritThrowingChance, 25, "Rogue throwing crit bonus")
RULE_INT ( Combat, RogueDeadlyStrikeChance, 80, "Rogue chance throwing from behind crit becomes a deadly strike")
RULE_INT ( Combat, RogueDeadlyStrikeMod, 2, "Deadly strike modifier to crit damage")
RULE_INT ( Combat, ClientBaseCritChance, 0, "The base crit chance for all clients, this will stack with warrior's/zerker's crit chance.")
RULE_BOOL ( Combat, EnableFearPathing, true, "")
RULE_INT ( Combat, FleeHPRatio, 20, "HP % under which an NPC starts to flee.")
RULE_BOOL ( Combat, FleeIfNotAlone, false, "If false, mobs won't flee if other mobs are in combat with it.")
RULE_INT ( Combat, MinRangedAttackDist, 25, "Minimum Distance to use Ranged Attacks")
RULE_BOOL ( Combat, ArcheryBonusRequiresStationary, true, "does the 2x archery bonus chance require a stationary npc")
RULE_REAL ( Combat, ArcheryBaseDamageBonus, 1, "% Modifier to Base Archery Damage (.5 = 50% base damage, 1 = 100%, 2 = 200%)")
RULE_REAL ( Combat, ArcheryNPCMultiplier, 1.0, "this is multiplied by the regular dmg to get the archery dmg")
RULE_BOOL ( Combat, AssistNoTargetSelf, true, "when assisting a target that does not have a target: true = target self, false = leave target as was before assist (false = live like)")
RULE_INT ( Combat, MinHastedDelay, 400, "how fast we can get with haste.")
RULE_INT ( Combat, NPCFlurryChance, 20, "Chance for NPC to flurry.")
RULE_BOOL (Combat,TauntOverLevel, 1, "Allows you to taunt NPC's over warriors level.")
RULE_INT ( Combat, QuiverWRHasteDiv, 4, "Weight Reduction is divided by this to get haste contribution for quivers")
RULE_BOOL ( Combat, UseArcheryBonusRoll, false, "Make the 51+ archery bonus require an actual roll")
RULE_INT ( Combat, ArcheryBonusChance, 50, "")
RULE_INT ( Combat, BerserkerFrenzyStart, 35, "")
RULE_INT ( Combat, BerserkerFrenzyEnd, 45, "")
RULE_BOOL ( Combat, UseDiscTimerGroups, false, "After about 2004 or so, disc reuse timers were combined into groups so multiple discs could be used in succession.")
RULE_INT ( Combat, PvPMeleeDmgPct, 100, "Percentage of melee damage done in PvP combat. 100 = 100% i.e. unmodified")
RULE_INT ( Combat, PvPSpellDmgPct, 62, "Percentage of spell damage done in PvP combat. 100 = 100% i.e. unmodified")
RULE_INT ( Combat, PvPArcheryDmgPct, 100, "Percentage of archery damage done in PvP combat. 100 = 100% i.e. unmodified")
RULE_CATEGORY_END()

RULE_CATEGORY( NPC )
RULE_INT ( NPC, MinorNPCCorpseDecayTimeMS, 480000, "level<55")
RULE_INT ( NPC, MajorNPCCorpseDecayTimeMS, 1800000, "level>=55")
RULE_INT ( NPC, CorpseUnlockTimer, 120000, "")
RULE_INT ( NPC, EmptyNPCCorpseDecayTimeMS, 30000, "")
RULE_INT ( NPC, SayPauseTimeInSec, 70, "")
RULE_INT ( NPC, OOCRegen, 0, "")
RULE_BOOL ( NPC, BuffFriends, true, "")
RULE_INT ( NPC, LastFightingDelayMovingMin, 3000, "")
RULE_INT ( NPC, LastFightingDelayMovingMax, 26000, "")
RULE_BOOL ( NPC, ReturnNonQuestItems, true, "Returns items on NPCs that don't have an EVENT_TRADE sub in their script or have KOS faction with the player")
RULE_INT ( NPC, StartEnrageValue, 10, "% HP that an NPC will begin to enrage")
RULE_BOOL ( NPC, LiveLikeEnrage, false, "If set to true then only player controlled pets will enrage") 
RULE_INT ( NPC, NPCTemplateID, 541, "MonsterSum1")
RULE_BOOL ( NPC, BoatsRunByDefault, true, "Mainly to make it easier to adjust boats' timing on the fly.") 
RULE_BOOL(NPC, CheckSoWBuff, false, "")
RULE_BOOL( NPC, IgnoreQuestLoot, false, "")
RULE_INT( NPC, NPCToNPCAggroTimerMin, 500, "Minimum time span after which one NPC aggro another NPC (milliseconds)")
RULE_INT( NPC, NPCToNPCAggroTimerMax, 6000, "Maximum time span after which one NPC aggro another NPC (milliseconds)")
RULE_CATEGORY_END()

RULE_CATEGORY ( Aggro )
RULE_INT ( Aggro, SpellAggroMod, 100, "")
RULE_REAL ( Aggro, TunnelVisionAggroMod, 0.75, "people not currently the top hate generate this much hate on a Tunnel Vision mob")
RULE_INT(Aggro, ClientAggroCheckMovingInterval, 1000, "Interval in which clients actually check for aggro while moving - in milliseconds - this should be lower than ClientAggroCheckIdleInterval")
RULE_INT(Aggro, ClientAggroCheckIdleInterval, 3000, "Interval in which clients actually check for aggro while idle - in milliseconds - this should be higher than ClientAggroCheckMovingInterval")
RULE_CATEGORY_END()

RULE_CATEGORY ( Chat )
RULE_BOOL ( Chat, ServerWideOOC, false, "")
RULE_BOOL ( Chat, ServerWideAuction, false, "")
RULE_BOOL ( Chat, EnableMailKeyIPVerification, true, "")
RULE_BOOL ( Chat, EnableAntiSpam, true, "")
RULE_BOOL ( Chat, SuppressCommandErrors, false, "Do not suppress by default")
RULE_INT ( Chat, MinStatusToBypassAntiSpam, 100, "")
RULE_INT ( Chat, MinimumMessagesPerInterval, 20, "")
RULE_INT ( Chat, MaximumMessagesPerInterval, 50, "")
RULE_INT ( Chat, MaxMessagesBeforeKick, 60, "")
RULE_INT ( Chat, IntervalDurationMS, 60000, "")
RULE_INT ( Chat, KarmaUpdateIntervalMS, 120000, "")
RULE_INT ( Chat, KarmaGlobalChatLevelLimit, 8, "level limit you need to of reached to talk in ooc/auction/chat if your karma is too low.")
RULE_INT ( Chat, KarmaGlobalChatLimit, 12, "amount of karma you need to be able to talk in ooc/auction/chat below the level limit")
RULE_INT ( Chat, GlobalChatLevelLimit, 8, "level limit you need to of reached to talk in ooc/auction/chat if your karma is too low.")
RULE_STRING (Chat, ChatChannelDiscordRelayConfig, "", "The chat server can relay channel messages to discord in a one-way manner.  This requires URLs to be configured in the discord_webhooks table and configured here.  Format is: Channel1:webhook_id1,Channel2:webhookid2 for example 'General:15,Testing:16'.")
RULE_CATEGORY_END()

RULE_CATEGORY ( Merchant )
RULE_BOOL( Merchant, UseGreed, false, "if true, merchants that do not already have a database set greed value will raise their prices the more they are sold to or bought from.  This is custom behavior and not classic")
RULE_INT ( Merchant, GreedThreshold, 100, "How many purchases are required to or from a vendor before it becomes greedy and starts to raise their prices.")
RULE_BOOL( Merchant, ClearTempList, true, "clear temp list after death if set true.")
RULE_CATEGORY_END()

RULE_CATEGORY ( Bazaar )
RULE_INT ( Bazaar, MaxSearchResults, 50, "")
RULE_CATEGORY_END()

RULE_CATEGORY ( Channels )
RULE_INT ( Channels, RequiredStatusAdmin, 251, "Required status to administer chat channels")
RULE_INT ( Channels, RequiredStatusListAll, 251, "Required status to list all chat channels")
RULE_INT ( Channels, DeleteTimer, 1440, "Empty password protected channels will be deleted after this many minutes")
RULE_CATEGORY_END()

RULE_CATEGORY ( EventLog )
RULE_BOOL ( EventLog, RecordSellToMerchant, false, "Record sales from a player to an NPC merchant in eventlog table")
RULE_BOOL ( EventLog, RecordBuyFromMerchant, false, "Record purchases by a player from an NPC merchant in eventlog table")
RULE_BOOL ( EventLog, SkipCommonPacketLogging, true, " Doesn't log OP_MobHealth or OP_ClientUpdate")
RULE_CATEGORY_END()

RULE_CATEGORY ( AA )
RULE_INT ( AA, ExpPerPoint, 18750000, "Amount of exp per AA")
RULE_CATEGORY_END()

RULE_CATEGORY( Console )
RULE_INT ( Console, SessionTimeOut, 600000, "Amount of time in ms for the console session to time out")
RULE_CATEGORY_END()

RULE_CATEGORY(Network)
RULE_INT(Network, ResendDelayBaseMS, 100, "Base delay for resending data in EQStreamManager (milliseconds)")
RULE_REAL(Network, ResendDelayFactor, 1.5, "Multiplier for the base delay when resending data in EQStreamManager")
RULE_INT(Network, ResendDelayMinMS, 300, "Minimum timespan between two send retries (milliseconds)")
RULE_INT(Network, ResendDelayMaxMS, 5000, "Maximum timespan between two send retries (milliseconds)")
RULE_CATEGORY_END()

RULE_CATEGORY( QueryServ )
RULE_BOOL( QueryServ, PlayerLogChat, false, "Logs Player Chat") 
RULE_BOOL( QueryServ, PlayerLogTrades, false, "Logs Player Trades")
RULE_BOOL( QueryServ, PlayerLogNPCKills, false, "Logs Player NPC Kills")
RULE_BOOL( QueryServ, PlayerLogDeletes, false, "Logs Player Deletes")
RULE_BOOL( QueryServ, PlayerLogMoves, false, "Logs Player Moves")
RULE_BOOL( QueryServ, PlayerLogMerchantTransactions, false, "Logs Merchant Transactions")
RULE_BOOL( QueryServ, PlayerLogGroundSpawn, false, "Logs Player Dropped or Picked-up items.")
RULE_BOOL( QueryServ, PlayerLogZone, false, "Logs Player Zone Events")
RULE_BOOL( QueryServ, PlayerLogDeaths, false, "Logs Player Deaths")
RULE_BOOL( QueryServ, PlayerLogConnectDisconnect, false, "Logs Player Connect Disconnect State")
RULE_BOOL( QueryServ, PlayerLogLevels, false, "Logs Player Leveling/Deleveling")
RULE_BOOL( QueryServ, PlayerLogAARate, false, "Logs Player AA Experience Rates") 
RULE_BOOL( QueryServ, PlayerLogQGlobalUpdate, false, "Logs Player QGlobal Updates")
RULE_BOOL( QueryServ, PlayerLogAAPurchases, false, "Log Player AA Purchases")
RULE_BOOL( QueryServ, PlayerLogTradeSkillEvents, false, "Log Player Tradeskill Transactions")
RULE_BOOL( QueryServ, PlayerLogMoneyTransactions, false, "Log Player Money Transaction/Splits")
RULE_BOOL( QueryServ, PlayerLogBankTransactions, false, "Log Player Bank Transactions")
RULE_BOOL( QueryServ, PlayerLogLoot, false, "Log Player Looting cash/Items")
RULE_BOOL( QueryServ, PlayerLogItemDesyncs, true, "Log Player item desyncs")
RULE_BOOL( QueryServ, BazaarAuditTrail, true, "Log Bazaar transactions")
RULE_INT( QueryServ, LevelAlwaysLogKills, 1, "The NPC level where player kills are always logged. Below this only possible killsteals are logged. Set to 1 to disable.")
RULE_CATEGORY_END()

RULE_CATEGORY( Groundspawns )
RULE_INT ( Groundspawns, DecayTime, 300000, "Decay time of player dropped items. 5 minutes")
RULE_INT ( Groundspawns, DisarmDecayTime, 300000, "Decay time of weapons dropped due to disarm 5 minutes")
RULE_INT ( Groundspawns, FullInvDecayTime, ********, "Decay time of items dropped due to full inventory (trades/merchants) Default 6 hours.")
RULE_BOOL ( Groundspawns, RandomSpawn, true, "Determines if groundspawns with random spawn locs will periodically despawn and respawn elsewhere.")
RULE_CATEGORY_END()

RULE_CATEGORY( Range )
RULE_INT ( Range, EventSay, 5000, "")
RULE_INT ( Range, EventAggroSay, 5000, "")
RULE_INT ( Range, Say, 100, "")
RULE_INT ( Range, Emote, 135, "")
RULE_INT ( Range, BeginCast, 200, "")
RULE_INT ( Range, Anims, 135, "")
RULE_INT ( Range, ProjectileAnims, 135, "")
RULE_INT ( Range, SpellAnims, 135, "")
RULE_INT ( Range, DamageMessages, 75, "")
RULE_INT ( Range, CombatSpecials, 80, "")
RULE_INT ( Range, SpellMessages, 75, "")
RULE_INT ( Range, SongMessages, 60, "")
RULE_CATEGORY_END()

RULE_CATEGORY(Doors)
RULE_REAL(Doors, LargeRaceRange, 1180, "")
RULE_REAL(Doors, NormalRaceRange, 250, "")
RULE_REAL(Doors, LargeRaceZ, 150, "")
RULE_REAL(Doors, NormalRaceZ, 20, "")
RULE_CATEGORY_END()

RULE_CATEGORY(Items)
RULE_BOOL(Items, DisableNoDrop, false, "")
RULE_BOOL(Items, DisableNoRent, false, "")
RULE_CATEGORY_END()

RULE_CATEGORY(Bugs)
RULE_BOOL(Bugs, ReportingSystemActive, true, "Activates bug reporting")
RULE_CATEGORY_END()

RULE_CATEGORY(Petitions)
RULE_BOOL(Petitions, PetitionSystemActive, false, "Activates bug reporting")
RULE_CATEGORY_END()

RULE_CATEGORY(Expansion)
RULE_INT(Expansion, CurrentExpansion, -1, "The current expansion enabled for the server [-1 = All Expansion, 0 = Classic, 1 = Kunark etc.]")
RULE_BOOL(Expansion, UseItemExpansionSetting, true, "Disabled focus effect, skill mod, bane, and level rec/req on items for appropiate expansion setting")
RULE_BOOL(Expansion, UseExperienceExpansionSetting, true, "Set ZEM for appropiate expansion setting")
RULE_CATEGORY_END()

RULE_CATEGORY(Analytics)
RULE_BOOL(Analytics, CrashReporting, true, "Automatic crash reporting analytics for EQEmu Server developers")
RULE_BOOL(Analytics, GenerateCore, false, "Generate core dump file when crashes occur")
RULE_CATEGORY_END()

RULE_CATEGORY(Logging)
RULE_BOOL(Logging, PrintFileFunctionAndLine, false, "Ex: [World Server] [net.cpp::main:309] Loading variables...")
RULE_BOOL(Logging, WorldGMSayLogging, true, "Relay worldserver logging to zone processes via GM say output")
RULE_BOOL(Logging, PlayerEventsQSProcess, false, "Have query server process player events instead of world. Useful when wanting to use a dedicated server and database for processing player events on separate disk")
RULE_INT(Logging, BatchPlayerEventProcessIntervalSeconds, 5, "This is the interval in which player events are processed in world or qs")
RULE_INT(Logging, BatchPlayerEventProcessChunkSize, 10000, "This is the cap of events that can be inserted into the queue before a force flush. This is to keep from hitting MySQL max_allowed_packet and killing the connection")
RULE_CATEGORY_END()

RULE_CATEGORY(HotReload)
RULE_BOOL(HotReload, QuestsRepopWithReload, true, "When a hot reload is triggered, the zone will repop")
RULE_BOOL(HotReload, QuestsRepopWhenPlayersNotInCombat, true, "When a hot reload is triggered, the zone will repop when no clients are in combat")
RULE_BOOL(HotReload, QuestsResetTimersWithReload, true, "When a hot reload is triggered, quest timers will be reset")
RULE_CATEGORY_END()

RULE_CATEGORY(Cheat)
RULE_REAL(Cheat, MQWarpDetectionDistanceFactor, 9.0, "clients move at 4.4 about if in a straight line but with movement and to acct for lag we raise it a bit")
RULE_INT(Cheat, MQWarpExemptStatus, -1, "Required status level to exempt the MQWarpDetector. Set to -1 to disable this feature.")
RULE_INT(Cheat, MQZoneExemptStatus, -1, "Required status level to exempt the MQZoneDetector. Set to -1 to disable this feature.")
RULE_INT(Cheat, MQGateExemptStatus, -1, "Required status level to exempt the MQGateDetector. Set to -1 to disable this feature.")
RULE_INT(Cheat, MQGhostExemptStatus, -1, "Required status level to exempt the MQGhostDetector. Set to -1 to disable this feature.")
RULE_INT(Cheat, MQFastMemExemptStatus, -1, "Required status level to exempt the MQFastMemDetector. Set to -1 to disable this feature.")
RULE_BOOL(Cheat, EnableMQWarpDetector, false, "Enable the MQWarp Detector. Set to False to disable this feature.")
RULE_BOOL(Cheat, EnableMQZoneDetector, true, "Enable the MQZone Detector. Set to False to disable this feature.")
RULE_BOOL(Cheat, EnableMQGateDetector, true, "Enable the MQGate Detector. Set to False to disable this feature.")
RULE_BOOL(Cheat, EnableMQGhostDetector, false, "Enable the MQGhost Detector. Set to False to disable this feature.")
RULE_BOOL(Cheat, EnableMQFastMemDetector, true, "Enable the MQFastMem Detector. Set to False to disable this feature.")
RULE_BOOL(Cheat, MarkMQWarpLT, false, "Mark clients makeing smaller warps")
RULE_CATEGORY_END()

#undef RULE_CATEGORY
#undef RULE_INT
#undef RULE_REAL
#undef RULE_BOOL
#undef RULE_STRING
#undef RULE_CATEGORY_END
