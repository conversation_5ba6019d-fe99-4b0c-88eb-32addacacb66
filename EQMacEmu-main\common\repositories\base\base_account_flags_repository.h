/**
 * DO NOT MODIFY THIS FILE
 *
 * This repository was automatically generated and is NOT to be modified directly.
 * Any repository modifications are meant to be made to the repository extending the base.
 * Any modifications to base repositories are to be made by the generator only
 *
 * @generator ./utils/scripts/generators/repository-generator.pl
 * @docs https://eqemu.gitbook.io/server/in-development/developer-area/repositories
 */

#ifndef EQEMU_BASE_ACCOUNT_FLAGS_REPOSITORY_H
#define EQEMU_BASE_ACCOUNT_FLAGS_REPOSITORY_H

#include "../../database.h"
#include "../../strings.h"
#include <ctime>

class BaseAccountFlagsRepository {
public:
	struct AccountFlags {
		uint32_t    p_accid;
		std::string p_flag;
		std::string p_value;
	};

	static std::string PrimaryKey()
	{
		return std::string("p_accid");
	}

	static std::vector<std::string> Columns()
	{
		return {
			"p_accid",
			"p_flag",
			"p_value",
		};
	}

	static std::vector<std::string> SelectColumns()
	{
		return {
			"p_accid",
			"p_flag",
			"p_value",
		};
	}

	static std::string ColumnsRaw()
	{
		return std::string(Strings::Implode(", ", Columns()));
	}

	static std::string SelectColumnsRaw()
	{
		return std::string(Strings::Implode(", ", SelectColumns()));
	}

	static std::string TableName()
	{
		return std::string("account_flags");
	}

	static std::string BaseSelect()
	{
		return fmt::format(
			"SELECT {} FROM {}",
			SelectColumnsRaw(),
			TableName()
		);
	}

	static std::string BaseInsert()
	{
		return fmt::format(
			"INSERT INTO {} ({}) ",
			TableName(),
			ColumnsRaw()
		);
	}

	static AccountFlags NewEntity()
	{
		AccountFlags e{};

		e.p_accid = 0;
		e.p_flag  = "";
		e.p_value = "";

		return e;
	}

	static AccountFlags GetAccountFlags(
		const std::vector<AccountFlags> &account_flagss,
		int account_flags_id
	)
	{
		for (auto &account_flags : account_flagss) {
			if (account_flags.p_accid == account_flags_id) {
				return account_flags;
			}
		}

		return NewEntity();
	}

	static AccountFlags FindOne(
		Database& db,
		int account_flags_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {} = {} LIMIT 1",
				BaseSelect(),
				PrimaryKey(),
				account_flags_id
			)
		);

		auto row = results.begin();
		if (results.RowCount() == 1) {
			AccountFlags e{};

			e.p_accid = row[0] ? static_cast<uint32_t>(strtoul(row[0], nullptr, 10)) : 0;
			e.p_flag  = row[1] ? row[1] : "";
			e.p_value = row[2] ? row[2] : "";

			return e;
		}

		return NewEntity();
	}

	static int DeleteOne(
		Database& db,
		int account_flags_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {} = {}",
				TableName(),
				PrimaryKey(),
				account_flags_id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int UpdateOne(
		Database& db,
		const AccountFlags &e
	)
	{
		std::vector<std::string> v;

		auto columns = Columns();

		v.push_back(columns[0] + " = " + std::to_string(e.p_accid));
		v.push_back(columns[1] + " = '" + Strings::Escape(e.p_flag) + "'");
		v.push_back(columns[2] + " = '" + Strings::Escape(e.p_value) + "'");

		auto results = db.QueryDatabase(
			fmt::format(
				"UPDATE {} SET {} WHERE {} = {}",
				TableName(),
				Strings::Implode(", ", v),
				PrimaryKey(),
				e.p_accid
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static AccountFlags InsertOne(
		Database& db,
		AccountFlags e
	)
	{
		std::vector<std::string> v;

		v.push_back(std::to_string(e.p_accid));
		v.push_back("'" + Strings::Escape(e.p_flag) + "'");
		v.push_back("'" + Strings::Escape(e.p_value) + "'");

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES ({})",
				BaseInsert(),
				Strings::Implode(",", v)
			)
		);

		if (results.Success()) {
			e.p_accid = results.LastInsertedID();
			return e;
		}

		e = NewEntity();

		return e;
	}

	static int InsertMany(
		Database& db,
		const std::vector<AccountFlags> &entries
	)
	{
		std::vector<std::string> insert_chunks;

		for (auto &e: entries) {
			std::vector<std::string> v;

			v.push_back(std::to_string(e.p_accid));
			v.push_back("'" + Strings::Escape(e.p_flag) + "'");
			v.push_back("'" + Strings::Escape(e.p_value) + "'");

			insert_chunks.push_back("(" + Strings::Implode(",", v) + ")");
		}

		std::vector<std::string> v;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES {}",
				BaseInsert(),
				Strings::Implode(",", insert_chunks)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static std::vector<AccountFlags> All(Database& db)
	{
		std::vector<AccountFlags> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{}",
				BaseSelect()
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			AccountFlags e{};

			e.p_accid = row[0] ? static_cast<uint32_t>(strtoul(row[0], nullptr, 10)) : 0;
			e.p_flag  = row[1] ? row[1] : "";
			e.p_value = row[2] ? row[2] : "";

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static std::vector<AccountFlags> GetWhere(Database& db, const std::string &where_filter)
	{
		std::vector<AccountFlags> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {}",
				BaseSelect(),
				where_filter
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			AccountFlags e{};

			e.p_accid = row[0] ? static_cast<uint32_t>(strtoul(row[0], nullptr, 10)) : 0;
			e.p_flag  = row[1] ? row[1] : "";
			e.p_value = row[2] ? row[2] : "";

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static int DeleteWhere(Database& db, const std::string &where_filter)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {}",
				TableName(),
				where_filter
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int Truncate(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"TRUNCATE TABLE {}",
				TableName()
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int64 GetMaxId(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COALESCE(MAX({}), 0) FROM {}",
				PrimaryKey(),
				TableName()
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

	static int64 Count(Database& db, const std::string &where_filter = "")
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COUNT(*) FROM {} {}",
				TableName(),
				(where_filter.empty() ? "" : "WHERE " + where_filter)
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

};

#endif //EQEMU_BASE_ACCOUNT_FLAGS_REPOSITORY_H
