/*	EQEMu: Everquest Server Emulator
	Copyright (C) 2001-2002 EQEMu Development Team (http://eqemu.org)

	This program is free software; you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation; version 2 of the License.

	This program is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY except by those people which sell it, which
	are required to give you total support for your newly bought product;
	without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
	A PARTICULAR PURPOSE. See the GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with this program; if not, write to the Free Software
	Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA
*/
#ifndef WORLDSERVER_H
#define WORLDSERVER_H

#include <mutex>
#include "../common/eq_packet_structs.h"
#include "../common/net/servertalk_client_connection.h"

class WorldServer {
public:
	WorldServer();
	~WorldServer();

	void Connect();
	bool SendPacket(ServerPacket* pack);
	std::string GetIP() const;
	uint16 GetPort() const;
	bool Connected() const;

	void HandleMessage(uint16 opcode, const EQ::Net::Packet &p);
private:
	std::unique_ptr<EQ::Net::ServertalkClient> m_connection;

};
#endif

