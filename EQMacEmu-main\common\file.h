/**
 * EQEmulator: Everquest Server Emulator
 * Copyright (C) 2001-2020 EQEmulator Development Team (https://github.com/EQEmu/Server)
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; version 2 of the License.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY except by those people which sell it, which
 * are required to give you total support for your newly bought product;
 * without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA
 *
*/

#ifndef EQEMU_FILE_H
#define EQEMU_FILE_H

#include <filesystem>

namespace fs = std::filesystem;

struct FileContentsResult {
	std::string contents;
	std::string error;
};

class File {
public:
	static bool Exists(const std::string& name);
	static void Makedir(const std::string& directory_name);
	static FileContentsResult GetContents(const std::string& file_name);
	static std::string FindEqemuConfigPath();
	static std::string GetCwd();
};

bool Exists(const std::string& name);

#endif //EQEMU_FILE_H