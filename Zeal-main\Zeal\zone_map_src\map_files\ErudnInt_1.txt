P -806.9344, -712.0956, 0.6260, 255, 0, 0, 3, to_<PERSON><PERSON><PERSON>
P 0.0000, 0.0000, 0.0000, 255, 0, 0, 2, Succor
P -855.7394, -728.7767, -15.9980, 255, 210, 0, 2, <PERSON><PERSON><PERSON><PERSON><PERSON>_(<PERSON><PERSON>)
P -835.1929, -781.0613, -15.9980, 255, 210, 0, 2, <PERSON><PERSON><PERSON><PERSON><PERSON>_(<PERSON><PERSON>)
P -836.3702, -767.9595, -15.9980, 255, 210, 0, 2, <PERSON><PERSON>_<PERSON><PERSON>_(<PERSON><PERSON>)
P -776.0000, -537.0000, 84.7197, 128, 128, 128, 2, <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(GM_Enchanter)
P -744.0000, -509.0000, 51.7197, 128, 128, 128, 2, <PERSON><PERSON>_<PERSON><PERSON>_(GM_Enchanter)
P -712.0000, -887.0000, 84.7197, 128, 128, 128, 2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(<PERSON>_Wizard)
P -744.0000, -917.0000, 51.7197, 128, 128, 128, 2, <PERSON><PERSON><PERSON><PERSON><PERSON>_(<PERSON>_<PERSON>)
P -919.0000, -744.0000, 84.7197, 128, 128, 128, 2, <PERSON><PERSON><PERSON><PERSON>vin<PERSON>_(GM_<PERSON>ian)
P -912.0000, -742.0000, 84.7197, 128, 128, 128, 2, Re<PERSON>_G<PERSON>s_(<PERSON>_<PERSON>ian)
<PERSON> -904.0000, -713.0000, 67.7197, 128, 128, 128, 2, V<PERSON><PERSON>_<PERSON><PERSON>ir_(<PERSON>_<PERSON>ian)
P -921.0000, -711.0000, 51.7197, 128, 128, 128, 2, <PERSON><PERSON>_<PERSON>kus_(GM_Magician)
P -647.0000, -703.0000, 3.7510, 128, 128, 128, 2, Nolusia_(GM_Enchanter)
P -804.0000, -718.0000, 35.7510, 128, 128, 128, 2, Raskena_Djor_(GM_Wizard)
P -805.0000, -727.0000, 35.7510, 128, 128, 128, 2, Toresian_Fhabel_(GM_Enchanter)
P -777.0000, -810.0000, 35.7510, 128, 128, 128, 2, Whes_Nareya_(GM_Wizard)
P -804.0000, -703.0000, 35.7510, 0, 128, 0, 2, Telor_Beteria_(General_Spells)
P -538.9391, -654.8943, -15.9980, 0, 128, 0, 2, Sharin_Denuen_(Alchemy)
P -597.0000, -697.0000, -12.2488, 0, 128, 0, 2, Myrcin_Denuen_(Jewelcrafting)
P -574.0000, -667.0000, -12.2487, 0, 128, 0, 2, Elsbin_Denuen_(Jewelcrafting)
P -574.0000, -631.0000, -12.2487, 0, 128, 0, 2, Glysin_Denuen_(Jewelcrafting)
P -552.0000, -731.0000, -12.2486, 0, 128, 0, 2, Sothure_Gemcutter_(Jewelcrafting)
P -598.0000, -784.0000, -12.2481, 0, 128, 0, 2, Anite_Gemcutter_(Jewelcrafting)
P -829.0000, -667.0000, -12.2487, 0, 128, 0, 2, Tan_Monosty_(Satchels)
P -828.8675, -584.7206, -15.9980, 0, 128, 0, 2, Vall_Stonewisp_(Satchels)
P -828.8870, -686.8351, -15.9980, 0, 128, 0, 2, Judge_Monosty_(Satchels)
P -690.0000, -900.0000, 0.0000, 255, 255, 255, 2, Wizard_Guild
P -950.0000, -650.0000, 0.0000, 255, 255, 255, 2, Magician_Guild
P -760.0000, -480.0000, 0.0000, 255, 255, 255, 2, Enchanter_Guild
P -747.3798, -607.1277, 35.5592, 255, 255, 255, 2, Tower_of_the_Craft_Keepers_(Enchanters)
P -851.7919, -709.7433, 36.5553, 255, 255, 255, 2, Tower_of_the_Gate_Callers_(Mage)
P -746.0625, -822.7549, 35.5307, 255, 255, 255, 2, Tower_of_the_Crimson_Hands_(Wizards)
P -781.1705, -665.9545, -14.4772, 240, 240, 240, 2, Erudin_City_Office
P -626.6792, -665.6359, -14.4730, 240, 240, 240, 2, Vials_of_Vitality
P -629.5609, -764.1742, -13.4424, 240, 240, 240, 2, Sothure`s_Fine_Gems
P -778.7952, -761.4518, -14.4672, 240, 240, 240, 2, Bank_of_Erudin
P -655.5378, -691.3226, 3.4670, 128, 128, 128, 2, Nolusia_Finharn_(GM_Enchanter)
P -741.9131, -865.8752, 65.5003, 0, 128, 0, 2, Andane_Starinen_(Wizard_Supplies)
P -885.0000, -630.0000, -32.9970, 240, 0, 0, 2, from_The_Hole
P -772.3467, -743.6241, -15.9980, 0, 240, 0, 2, Sparlus_Penfold_(Parcels)
P -854.8950, -798.8802, -15.9980, 0, 0, 0, 2, Ienala_Eceiaiu
P -817.1376, -599.1570, -32.9980, 0, 0, 0, 2, Shondo_Billin
P -841.5110, -550.9933, -33.9980, 240, 240, 240, 2, Jail
P -743.4260, -779.9865, 34.0020, 0, 0, 0, 2, Syreth_the_Fathomkeeper
P -722.0034, -910.9747, 79.9707, 0, 127, 0, 2, Tika_Shockstep_(Wizard_Supplies)
P -722.2719, -916.6550, 47.9707, 0, 127, 0, 2, Effunic_Korett_(Wizard_Spells)
P -757.4667, -922.8979, 47.9707, 0, 127, 0, 2, Barstall_Methinon_(Wizard_Spells)
P -760.1839, -898.0648, 47.9707, 0, 0, 0, 2, Akbaq_Salid
P -802.0829, -761.9084, 32.0020, 0, 127, 0, 2, a_spell_research_merchant_(Research)
P -824.3494, -636.5521, 32.0020, 0, 127, 0, 2, a_spell_reseach_merchant_(Research)
P -648.6440, -647.7073, 32.0020, 0, 127, 0, 2, a_spell_research_merchant_(Research)
P -679.7261, -709.9202, 32.0020, 0, 0, 0, 2, Barodreth_Firefingers
P -694.6935, -739.2943, 33.0020, 0, 127, 0, 2, a_spell_research_merchant_(Research)
P -915.7501, -695.8099, 47.9707, 0, 127, 0, 2, Killi_Frillep_(Magician_Spells)
P -949.7672, -697.4733, 47.9707, 0, 127, 0, 2, Onyssa_Vroce_(Magician_Spells)
P -953.5524, -725.0164, 47.9707, 0, 127, 0, 2, Kestall_Tulenci_(Magician_Spells)
P -920.0021, -726.5391, 47.9707, 0, 127, 0, 2, Tuballi_Stellari_(Magician_Spells)
P -907.8091, -706.1129, 63.9707, 0, 127, 0, 2, Harban_Ranflash_(Magician_Supplies)
P -925.8887, -736.1196, 80.9707, 0, 0, 0, 2, Chief_Adair
P -949.6398, -735.9916, 79.9707, 0, 127, 0, 2, Frena_Runeflash_(Jewelcrafting)
P -950.6771, -709.0016, 79.9707, 0, 127, 0, 2, Wistcona_(Magician_Spells)
P -729.3664, -541.6683, 47.9707, 0, 127, 0, 2, Horacia_Garnellia_(Enchanter_Spells)
P -726.3362, -510.2691, 47.9707, 0, 127, 0, 2, Vellenon_Harrance_(Enchanter_Spells)
P -757.9655, -504.0167, 47.9707, 0, 127, 0, 2, Danilla_Delucic_(Enchanter_Spells)
P -760.5248, -530.4854, 47.9707, 0, 127, 0, 2, Estra_Nancer_(Enchanter_Spells)
P -749.2844, -550.4990, 63.9707, 0, 127, 0, 2, Gwynora_Armista_(Enchanter_Supplies)
P -739.9651, -566.9005, 63.9707, 0, 127, 0, 2, Pinilla_(Enchanter_Spells)
P -726.9003, -532.1802, 63.9707, 0, 0, 0, 2, Trilani_Parlone
P -771.8541, -508.9019, 79.9707, 0, 127, 0, 2, Jeliala_Glimmercharm_(Enchanter_Spells)
P -751.0251, -505.2601, 79.9707, 0, 127, 0, 2, Elentar_Bealtarik_(Enchanter_Spells)
P -558.8605, -765.5731, -15.9980, 0, 0, 0, 2, Mairee_Silentone
P -856.2845, -553.9502, -33.9980, 0, 0, 0, 2, Warden_Lius
P -837.3943, -726.7767, -15.9980, 0, 0, 0, 2, Agryn_Moonfield
P -859.1595, -750.6943, -15.9980, 0, 240, 0, 2, Sentinel_Knox_(Parcels)
P -738.6134, -699.2330, 40.6100, 0, 0, 240, 1, GS:_Old_Silver_Coin
