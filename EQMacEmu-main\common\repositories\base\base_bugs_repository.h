/**
 * DO NOT MODIFY THIS FILE
 *
 * This repository was automatically generated and is NOT to be modified directly.
 * Any repository modifications are meant to be made to the repository extending the base.
 * Any modifications to base repositories are to be made by the generator only
 *
 * @generator ./utils/scripts/generators/repository-generator.pl
 * @docs https://eqemu.gitbook.io/server/in-development/developer-area/repositories
 */

#ifndef EQEMU_BASE_BUGS_REPOSITORY_H
#define EQEMU_BASE_BUGS_REPOSITORY_H

#include "../../database.h"
#include "../../strings.h"
#include <ctime>


class BaseBugsRepository {
public:
	struct Bugs {
		uint32_t    id;
		std::string zone;
		std::string name;
		std::string ui;
		float       x;
		float       y;
		float       z;
		std::string type;
		uint32_t    flag;
		std::string target;
		std::string bug;
		std::string date;
		uint8_t     status;
		int8_t      _can_duplicate;
		int8_t      _crash_bug;
		int8_t      _target_info;
		int8_t      _character_flags;
		int8_t      _unknown_value;
	};

	static std::string PrimaryKey()
	{
		return std::string("id");
	}

	static std::vector<std::string> Columns()
	{
		return {
			"id",
			"zone",
			"name",
			"ui",
			"x",
			"y",
			"z",
			"type",
			"flag",
			"target",
			"bug",
			"date",
			"status",
			"_can_duplicate",
			"_crash_bug",
			"_target_info",
			"_character_flags",
			"_unknown_value",
		};
	}

	static std::vector<std::string> SelectColumns()
	{
		return {
			"id",
			"zone",
			"name",
			"ui",
			"x",
			"y",
			"z",
			"type",
			"flag",
			"target",
			"bug",
			"date",
			"status",
			"_can_duplicate",
			"_crash_bug",
			"_target_info",
			"_character_flags",
			"_unknown_value",
		};
	}

	static std::string ColumnsRaw()
	{
		return std::string(Strings::Implode(", ", Columns()));
	}

	static std::string SelectColumnsRaw()
	{
		return std::string(Strings::Implode(", ", SelectColumns()));
	}

	static std::string TableName()
	{
		return std::string("bugs");
	}

	static std::string BaseSelect()
	{
		return fmt::format(
			"SELECT {} FROM {}",
			SelectColumnsRaw(),
			TableName()
		);
	}

	static std::string BaseInsert()
	{
		return fmt::format(
			"INSERT INTO {} ({}) ",
			TableName(),
			ColumnsRaw()
		);
	}

	static Bugs NewEntity()
	{
		Bugs e{};

		e.id               = 0;
		e.zone             = "";
		e.name             = "";
		e.ui               = "";
		e.x                = 0;
		e.y                = 0;
		e.z                = 0;
		e.type             = "";
		e.flag             = 0;
		e.target           = "";
		e.bug              = "";
		e.date             = 0;
		e.status           = 0;
		e._can_duplicate   = 0;
		e._crash_bug       = 0;
		e._target_info     = 0;
		e._character_flags = 0;
		e._unknown_value   = 0;

		return e;
	}

	static Bugs GetBugs(
		const std::vector<Bugs> &bugss,
		int bugs_id
	)
	{
		for (auto &bugs : bugss) {
			if (bugs.id == bugs_id) {
				return bugs;
			}
		}

		return NewEntity();
	}

	static Bugs FindOne(
		Database& db,
		int bugs_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {} = {} LIMIT 1",
				BaseSelect(),
				PrimaryKey(),
				bugs_id
			)
		);

		auto row = results.begin();
		if (results.RowCount() == 1) {
			Bugs e{};

			e.id               = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.zone             = row[1] ? row[1] : "";
			e.name             = row[2] ? row[2] : "";
			e.ui               = row[3] ? row[3] : "";
			e.x                = strtof(row[4], nullptr);
			e.y                = strtof(row[5], nullptr);
			e.z                = strtof(row[6], nullptr);
			e.type             = row[7] ? row[7] : "";
			e.flag             = static_cast<uint32_t>(strtoul(row[8], nullptr, 10));
			e.target           = row[9] ? row[9] : "";
			e.bug              = row[10] ? row[10] : "";
			e.date             = row[11] ? row[11] : "";
			e.status           = static_cast<uint8_t>(strtoul(row[12], nullptr, 10));
			e._can_duplicate   = static_cast<int8_t>(atoi(row[13]));
			e._crash_bug       = static_cast<int8_t>(atoi(row[14]));
			e._target_info     = static_cast<int8_t>(atoi(row[15]));
			e._character_flags = static_cast<int8_t>(atoi(row[16]));
			e._unknown_value   = static_cast<int8_t>(atoi(row[17]));

			return e;
		}

		return NewEntity();
	}

	static int DeleteOne(
		Database& db,
		int bugs_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {} = {}",
				TableName(),
				PrimaryKey(),
				bugs_id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int UpdateOne(
		Database& db,
		const Bugs &e
	)
	{
		std::vector<std::string> v;

		auto columns = Columns();

		v.push_back(columns[1] + " = '" + Strings::Escape(e.zone) + "'");
		v.push_back(columns[2] + " = '" + Strings::Escape(e.name) + "'");
		v.push_back(columns[3] + " = '" + Strings::Escape(e.ui) + "'");
		v.push_back(columns[4] + " = " + std::to_string(e.x));
		v.push_back(columns[5] + " = " + std::to_string(e.y));
		v.push_back(columns[6] + " = " + std::to_string(e.z));
		v.push_back(columns[7] + " = '" + Strings::Escape(e.type) + "'");
		v.push_back(columns[8] + " = " + std::to_string(e.flag));
		v.push_back(columns[9] + " = '" + Strings::Escape(e.target) + "'");
		v.push_back(columns[10] + " = '" + Strings::Escape(e.bug) + "'");
		v.push_back(columns[11] + " = '" + Strings::Escape(e.date) + "'");
		v.push_back(columns[12] + " = " + std::to_string(e.status));
		v.push_back(columns[13] + " = " + std::to_string(e._can_duplicate));
		v.push_back(columns[14] + " = " + std::to_string(e._crash_bug));
		v.push_back(columns[15] + " = " + std::to_string(e._target_info));
		v.push_back(columns[16] + " = " + std::to_string(e._character_flags));
		v.push_back(columns[17] + " = " + std::to_string(e._unknown_value));

		auto results = db.QueryDatabase(
			fmt::format(
				"UPDATE {} SET {} WHERE {} = {}",
				TableName(),
				Strings::Implode(", ", v),
				PrimaryKey(),
				e.id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static Bugs InsertOne(
		Database& db,
		Bugs e
	)
	{
		std::vector<std::string> v;

		v.push_back(std::to_string(e.id));
		v.push_back("'" + Strings::Escape(e.zone) + "'");
		v.push_back("'" + Strings::Escape(e.name) + "'");
		v.push_back("'" + Strings::Escape(e.ui) + "'");
		v.push_back(std::to_string(e.x));
		v.push_back(std::to_string(e.y));
		v.push_back(std::to_string(e.z));
		v.push_back("'" + Strings::Escape(e.type) + "'");
		v.push_back(std::to_string(e.flag));
		v.push_back("'" + Strings::Escape(e.target) + "'");
		v.push_back("'" + Strings::Escape(e.bug) + "'");
		v.push_back("'" + Strings::Escape(e.date) + "'");
		v.push_back(std::to_string(e.status));
		v.push_back(std::to_string(e._can_duplicate));
		v.push_back(std::to_string(e._crash_bug));
		v.push_back(std::to_string(e._target_info));
		v.push_back(std::to_string(e._character_flags));
		v.push_back(std::to_string(e._unknown_value));

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES ({})",
				BaseInsert(),
				Strings::Implode(",", v)
			)
		);

		if (results.Success()) {
			e.id = results.LastInsertedID();
			return e;
		}

		e = NewEntity();

		return e;
	}

	static int InsertMany(
		Database& db,
		const std::vector<Bugs> &entries
	)
	{
		std::vector<std::string> insert_chunks;

		for (auto &e: entries) {
			std::vector<std::string> v;

			v.push_back(std::to_string(e.id));
			v.push_back("'" + Strings::Escape(e.zone) + "'");
			v.push_back("'" + Strings::Escape(e.name) + "'");
			v.push_back("'" + Strings::Escape(e.ui) + "'");
			v.push_back(std::to_string(e.x));
			v.push_back(std::to_string(e.y));
			v.push_back(std::to_string(e.z));
			v.push_back("'" + Strings::Escape(e.type) + "'");
			v.push_back(std::to_string(e.flag));
			v.push_back("'" + Strings::Escape(e.target) + "'");
			v.push_back("'" + Strings::Escape(e.bug) + "'");
			v.push_back("'" + Strings::Escape(e.date) + "'");
			v.push_back(std::to_string(e.status));
			v.push_back(std::to_string(e._can_duplicate));
			v.push_back(std::to_string(e._crash_bug));
			v.push_back(std::to_string(e._target_info));
			v.push_back(std::to_string(e._character_flags));
			v.push_back(std::to_string(e._unknown_value));

			insert_chunks.push_back("(" + Strings::Implode(",", v) + ")");
		}

		std::vector<std::string> v;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES {}",
				BaseInsert(),
				Strings::Implode(",", insert_chunks)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static std::vector<Bugs> All(Database& db)
	{
		std::vector<Bugs> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{}",
				BaseSelect()
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			Bugs e{};

			e.id               = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.zone             = row[1] ? row[1] : "";
			e.name             = row[2] ? row[2] : "";
			e.ui               = row[3] ? row[3] : "";
			e.x                = strtof(row[4], nullptr);
			e.y                = strtof(row[5], nullptr);
			e.z                = strtof(row[6], nullptr);
			e.type             = row[7] ? row[7] : "";
			e.flag             = static_cast<uint32_t>(strtoul(row[8], nullptr, 10));
			e.target           = row[9] ? row[9] : "";
			e.bug              = row[10] ? row[10] : "";
			e.date             = row[11] ? row[11] : "";
			e.status           = static_cast<uint8_t>(strtoul(row[12], nullptr, 10));
			e._can_duplicate   = static_cast<int8_t>(atoi(row[13]));
			e._crash_bug       = static_cast<int8_t>(atoi(row[14]));
			e._target_info     = static_cast<int8_t>(atoi(row[15]));
			e._character_flags = static_cast<int8_t>(atoi(row[16]));
			e._unknown_value   = static_cast<int8_t>(atoi(row[17]));

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static std::vector<Bugs> GetWhere(Database& db, const std::string &where_filter)
	{
		std::vector<Bugs> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {}",
				BaseSelect(),
				where_filter
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			Bugs e{};

			e.id               = static_cast<uint32_t>(strtoul(row[0], nullptr, 10));
			e.zone             = row[1] ? row[1] : "";
			e.name             = row[2] ? row[2] : "";
			e.ui               = row[3] ? row[3] : "";
			e.x                = strtof(row[4], nullptr);
			e.y                = strtof(row[5], nullptr);
			e.z                = strtof(row[6], nullptr);
			e.type             = row[7] ? row[7] : "";
			e.flag             = static_cast<uint32_t>(strtoul(row[8], nullptr, 10));
			e.target           = row[9] ? row[9] : "";
			e.bug              = row[10] ? row[10] : "";
			e.date             = row[11] ? row[11] : "";
			e.status           = static_cast<uint8_t>(strtoul(row[12], nullptr, 10));
			e._can_duplicate   = static_cast<int8_t>(atoi(row[13]));
			e._crash_bug       = static_cast<int8_t>(atoi(row[14]));
			e._target_info     = static_cast<int8_t>(atoi(row[15]));
			e._character_flags = static_cast<int8_t>(atoi(row[16]));
			e._unknown_value   = static_cast<int8_t>(atoi(row[17]));

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static int DeleteWhere(Database& db, const std::string &where_filter)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {}",
				TableName(),
				where_filter
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int Truncate(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"TRUNCATE TABLE {}",
				TableName()
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int64 GetMaxId(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COALESCE(MAX({}), 0) FROM {}",
				PrimaryKey(),
				TableName()
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

	static int64 Count(Database& db, const std::string &where_filter = "")
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COUNT(*) FROM {} {}",
				TableName(),
				(where_filter.empty() ? "" : "WHERE " + where_filter)
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

};

#endif //EQEMU_BASE_BUGS_REPOSITORY_H
