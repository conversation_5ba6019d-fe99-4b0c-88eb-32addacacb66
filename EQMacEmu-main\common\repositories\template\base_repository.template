/**
 * DO NOT MODIFY THIS FILE
 *
 * This repository was automatically generated and is NOT to be modified directly.
 * Any repository modifications are meant to be made to the repository extending the base.
 * Any modifications to base repositories are to be made by the generator only
 *
 * @generator ./utils/scripts/generators/repository-generator.pl
 * @docs https://eqemu.gitbook.io/server/in-development/developer-area/repositories
 */

#ifndef EQEMU_BASE_{{TABLE_NAME_UPPER}}_REPOSITORY_H
#define EQEMU_BASE_{{TABLE_NAME_UPPER}}_REPOSITORY_H

#include "../../database.h"
#include "../../strings.h"
#include <ctime>
{{ADDITIONAL_INCLUDES}}

class Base{{TABLE_NAME_CLASS}}Repository {
public:
	struct {{TABLE_NAME_STRUCT}} {
{{TABLE_STRUCT_COLUMNS}}
	};

	static std::string PrimaryKey()
	{
		return std::string("{{PRIMARY_KEY_STRING}}");
	}

	static std::vector<std::string> Columns()
	{
		return {
{{COLUMNS_LIST_QUOTED}}
		};
	}

	static std::vector<std::string> SelectColumns()
	{
		return {
{{SELECT_COLUMNS_LIST_QUOTED}}
		};
	}

	static std::string ColumnsRaw()
	{
		return std::string(Strings::Implode(", ", Columns()));
	}

	static std::string SelectColumnsRaw()
	{
		return std::string(Strings::Implode(", ", SelectColumns()));
	}

	static std::string TableName()
	{
		return std::string("{{TABLE_NAME_VAR}}");
	}

	static std::string BaseSelect()
	{
		return fmt::format(
			"SELECT {} FROM {}",
			SelectColumnsRaw(),
			TableName()
		);
	}

	static std::string BaseInsert()
	{
		return fmt::format(
			"INSERT INTO {} ({}) ",
			TableName(),
			ColumnsRaw()
		);
	}

	static {{TABLE_NAME_STRUCT}} NewEntity()
	{
		{{TABLE_NAME_STRUCT}} e{};

{{DEFAULT_ENTRIES}}

		return e;
	}

	static {{TABLE_NAME_STRUCT}} Get{{TABLE_NAME_STRUCT}}(
		const std::vector<{{TABLE_NAME_STRUCT}}> &{{TABLE_NAME_VAR}}s,
		int {{TABLE_NAME_VAR}}_id
	)
	{
		for (auto &{{TABLE_NAME_VAR}} : {{TABLE_NAME_VAR}}s) {
			if ({{TABLE_NAME_VAR}}.{{PRIMARY_KEY_STRING}} == {{TABLE_NAME_VAR}}_id) {
				return {{TABLE_NAME_VAR}};
			}
		}

		return NewEntity();
	}

	static {{TABLE_NAME_STRUCT}} FindOne(
		Database& db,
		int {{TABLE_NAME_VAR}}_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {} = {} LIMIT 1",
				BaseSelect(),
				PrimaryKey(),
				{{TABLE_NAME_VAR}}_id
			)
		);

		auto row = results.begin();
		if (results.RowCount() == 1) {
			{{TABLE_NAME_STRUCT}} e{};

{{FIND_ONE_ENTRIES}}
			return e;
		}

		return NewEntity();
	}

	static int DeleteOne(
		Database& db,
		int {{TABLE_NAME_VAR}}_id
	)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {} = {}",
				TableName(),
				PrimaryKey(),
				{{TABLE_NAME_VAR}}_id
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int UpdateOne(
		Database& db,
		const {{TABLE_NAME_STRUCT}} &e
	)
	{
		std::vector<std::string> v;

		auto columns = Columns();

{{UPDATE_ONE_ENTRIES}}

		auto results = db.QueryDatabase(
			fmt::format(
				"UPDATE {} SET {} WHERE {} = {}",
				TableName(),
				Strings::Implode(", ", v),
				PrimaryKey(),
				e.{{PRIMARY_KEY_STRING}}
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static {{TABLE_NAME_STRUCT}} InsertOne(
		Database& db,
		{{TABLE_NAME_STRUCT}} e
	)
	{
		std::vector<std::string> v;

{{INSERT_ONE_ENTRIES}}

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES ({})",
				BaseInsert(),
				Strings::Implode(",", v)
			)
		);

		if (results.Success()) {
			e.{{PRIMARY_KEY_STRING}} = results.LastInsertedID();
			return e;
		}

		e = NewEntity();

		return e;
	}

	static int InsertMany(
		Database& db,
		const std::vector<{{TABLE_NAME_STRUCT}}> &entries
	)
	{
		std::vector<std::string> insert_chunks;

		for (auto &e: entries) {
			std::vector<std::string> v;

{{INSERT_MANY_ENTRIES}}

			insert_chunks.push_back("(" + Strings::Implode(",", v) + ")");
		}

		std::vector<std::string> v;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES {}",
				BaseInsert(),
				Strings::Implode(",", insert_chunks)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static std::vector<{{TABLE_NAME_STRUCT}}> All(Database& db)
	{
		std::vector<{{TABLE_NAME_STRUCT}}> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{}",
				BaseSelect()
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			{{TABLE_NAME_STRUCT}} e{};

{{ALL_ENTRIES}}

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static std::vector<{{TABLE_NAME_STRUCT}}> GetWhere(Database& db, const std::string &where_filter)
	{
		std::vector<{{TABLE_NAME_STRUCT}}> all_entries;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} WHERE {}",
				BaseSelect(),
				where_filter
			)
		);

		all_entries.reserve(results.RowCount());

		for (auto row = results.begin(); row != results.end(); ++row) {
			{{TABLE_NAME_STRUCT}} e{};

{{ALL_ENTRIES}}

			all_entries.push_back(e);
		}

		return all_entries;
	}

	static int DeleteWhere(Database& db, const std::string &where_filter)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"DELETE FROM {} WHERE {}",
				TableName(),
				where_filter
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int Truncate(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"TRUNCATE TABLE {}",
				TableName()
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int64 GetMaxId(Database& db)
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COALESCE(MAX({}), 0) FROM {}",
				PrimaryKey(),
				TableName()
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

	static int64 Count(Database& db, const std::string &where_filter = "")
	{
		auto results = db.QueryDatabase(
			fmt::format(
				"SELECT COUNT(*) FROM {} {}",
				TableName(),
				(where_filter.empty() ? "" : "WHERE " + where_filter)
			)
		);

		return (results.Success() && results.begin()[0] ? strtoll(results.begin()[0], nullptr, 10) : 0);
	}

	static std::string BaseReplace()
	{
		return fmt::format(
			"REPLACE INTO {} ({}) ",
			TableName(),
			ColumnsRaw()
		);
	}

	static int ReplaceOne(
		Database& db,
		const {{TABLE_NAME_STRUCT}} &e
	)
	{
		std::vector<std::string> v;

{{INSERT_ONE_ENTRIES}}

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES ({})",
				BaseReplace(),
				Strings::Implode(",", v)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}

	static int ReplaceMany(
		Database& db,
		const std::vector<{{TABLE_NAME_STRUCT}}> &entries
	)
	{
		std::vector<std::string> insert_chunks;

		for (auto &e: entries) {
			std::vector<std::string> v;

{{INSERT_MANY_ENTRIES}}

			insert_chunks.push_back("(" + Strings::Implode(",", v) + ")");
		}

		std::vector<std::string> v;

		auto results = db.QueryDatabase(
			fmt::format(
				"{} VALUES {}",
				BaseReplace(),
				Strings::Implode(",", insert_chunks)
			)
		);

		return (results.Success() ? results.RowsAffected() : 0);
	}
};

#endif //EQEMU_BASE_{{TABLE_NAME_UPPER}}_REPOSITORY_H
